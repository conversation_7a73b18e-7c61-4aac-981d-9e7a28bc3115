<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2213.77734375 1534" style="max-width: 2213.78px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#666666;stroke:#666666;}#my-svg .marker.cross{stroke:#666666;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#666666!important;stroke-width:0;stroke:#666666;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#666666;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#666666;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:#ffffff;border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .note&gt;*{fill:#fff!important;stroke:#999!important;stroke-width:1px!important;color:#333!important;}#my-svg .note span{fill:#fff!important;stroke:#999!important;stroke-width:1px!important;color:#333!important;}#my-svg .note tspan{fill:#333!important;}#my-svg .clientLayer&gt;*{fill:#e1f5fe!important;stroke:#81d4fa!important;stroke-width:2px!important;}#my-svg .clientLayer span{fill:#e1f5fe!important;stroke:#81d4fa!important;stroke-width:2px!important;}#my-svg .wsLayer&gt;*{fill:#e8f5e9!important;stroke:#a5d6a7!important;stroke-width:2px!important;}#my-svg .wsLayer span{fill:#e8f5e9!important;stroke:#a5d6a7!important;stroke-width:2px!important;}#my-svg .appLayer&gt;*{fill:#fff3e0!important;stroke:#ffe0b2!important;stroke-width:2px!important;}#my-svg .appLayer span{fill:#fff3e0!important;stroke:#ffe0b2!important;stroke-width:2px!important;}#my-svg .infraLayer&gt;*{fill:#f3e5f5!important;stroke:#e1bee7!important;stroke-width:2px!important;}#my-svg .infraLayer span{fill:#f3e5f5!important;stroke:#e1bee7!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="Infrastructure" class="cluster note infraLayer"><rect height="848" width="416.703125" y="678" x="8" style="fill:#f3e5f5 !important;stroke:#e1bee7 !important;stroke-width:2px !important"/><g transform="translate(144.53125, 678)" class="cluster-label"><foreignObject height="24" width="143.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#333 !important" class="nodeLabel"><p>Infrastructure Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="Application" class="cluster note appLayer"><rect height="1389" width="905.48046875" y="8" x="444.703125" style="fill:#fff3e0 !important;stroke:#ffe0b2 !important;stroke-width:2px !important"/><g transform="translate(834.802734375, 8)" class="cluster-label"><foreignObject height="24" width="125.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#333 !important" class="nodeLabel"><p>Application Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="WebSocket" class="cluster note wsLayer"><rect height="362" width="754.609375" y="832" x="1451.16796875" style="fill:#e8f5e9 !important;stroke:#a5d6a7 !important;stroke-width:2px !important"/><g transform="translate(1767.44921875, 832)" class="cluster-label"><foreignObject height="24" width="122.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#333 !important" class="nodeLabel"><p>WebSocket Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="Client" class="cluster note clientLayer"><rect height="258" width="437.23046875" y="524" x="1375.3515625" style="fill:#e1f5fe !important;stroke:#81d4fa !important;stroke-width:2px !important"/><g transform="translate(1550.552734375, 524)" class="cluster-label"><foreignObject height="24" width="86.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#333 !important" class="nodeLabel"><p>Client Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="Models" class="cluster note appLayer"><rect height="104" width="675.05078125" y="1268" x="655.1328125" style="fill:#fff3e0 !important;stroke:#ffe0b2 !important;stroke-width:2px !important"/><g transform="translate(968.267578125, 1268)" class="cluster-label"><foreignObject height="24" width="48.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#333 !important" class="nodeLabel"><p>Models</p></span></div></foreignObject></g></g><g data-look="classic" id="Broadcasting" class="cluster note appLayer"><rect height="233" width="251.84375" y="395" x="464.703125" style="fill:#fff3e0 !important;stroke:#ffe0b2 !important;stroke-width:2px !important"/><g transform="translate(544.7890625, 395)" class="cluster-label"><foreignObject height="24" width="91.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#333 !important" class="nodeLabel"><p>Broadcasting</p></span></div></foreignObject></g></g><g data-look="classic" id="EventSourcing" class="cluster note appLayer"><rect height="312" width="351.2109375" y="33" x="511.8984375" style="fill:#fff3e0 !important;stroke:#ffe0b2 !important;stroke-width:2px !important"/><g transform="translate(634.73046875, 33)" class="cluster-label"><foreignObject height="24" width="105.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#333 !important" class="nodeLabel"><p>Event Sourcing</p></span></div></foreignObject></g></g><g data-look="classic" id="ChannelTypes" class="cluster note wsLayer"><rect height="104" width="714.609375" y="1065" x="1471.16796875" style="fill:#e8f5e9 !important;stroke:#a5d6a7 !important;stroke-width:2px !important"/><g transform="translate(1777.65234375, 1065)" class="cluster-label"><foreignObject height="24" width="101.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#333 !important" class="nodeLabel"><p>Channel Types</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Browser_Echo_0" d="M1498.125,603L1498.125,607.167C1498.125,611.333,1498.125,619.667,1498.125,628C1498.125,636.333,1498.125,644.667,1498.125,653C1498.125,661.333,1498.125,669.667,1505.361,677.687C1512.598,685.707,1527.07,693.413,1534.306,697.267L1541.543,701.12"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MobileApp_Echo_0" d="M1693.43,603L1693.43,607.167C1693.43,611.333,1693.43,619.667,1693.43,628C1693.43,636.333,1693.43,644.667,1693.43,653C1693.43,661.333,1693.43,669.667,1686.193,677.687C1678.957,685.707,1664.485,693.413,1657.248,697.267L1650.012,701.12"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Echo_Reverb_0" d="M1595.777,757L1595.777,761.167C1595.777,765.333,1595.777,773.667,1595.777,782C1595.777,790.333,1595.777,798.667,1595.777,807C1595.777,815.333,1595.777,823.667,1595.777,831.333C1595.777,839,1595.777,846,1595.777,849.5L1595.777,853"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Reverb_Channels_0" d="M1595.777,911L1595.777,915.167C1595.777,919.333,1595.777,927.667,1595.777,935.333C1595.777,943,1595.777,950,1595.777,953.5L1595.777,957"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Aggregates_Events_0" d="M711.422,112L711.422,116.167C711.422,120.333,711.422,128.667,711.422,136.333C711.422,144,711.422,151,711.422,154.5L711.422,158"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Events_Reactors_0" d="M686.584,216L682.751,220.167C678.918,224.333,671.252,232.667,667.419,240.333C663.586,248,663.586,255,663.586,258.5L663.586,262"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Events_EventStore_0" d="M754.498,216L761.145,220.167C767.793,224.333,781.088,232.667,787.735,245.5C794.383,258.333,794.383,275.667,794.383,293C794.383,310.333,794.383,327.667,794.383,340.5C794.383,353.333,794.383,361.667,794.383,370C794.383,378.333,794.383,386.667,794.383,399.5C794.383,412.333,794.383,429.667,794.383,447C794.383,464.333,794.383,481.667,794.383,494.5C794.383,507.333,794.383,515.667,794.383,528.5C794.383,541.333,794.383,558.667,794.383,576C794.383,593.333,794.383,610.667,794.383,623.5C794.383,636.333,794.383,644.667,794.383,653C794.383,661.333,794.383,669.667,727.599,681.129C660.815,692.592,527.247,707.184,460.463,714.48L393.679,721.776"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Reactors_BroadcastEvents_0" d="M625.702,320L619.856,324.167C614.01,328.333,602.317,336.667,596.471,345C590.625,353.333,590.625,361.667,590.625,370C590.625,378.333,590.625,386.667,590.625,394.333C590.625,402,590.625,409,590.625,412.5L590.625,416"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BroadcastEvents_Queue_0" d="M590.625,474L590.625,478.167C590.625,482.333,590.625,490.667,590.625,499C590.625,507.333,590.625,515.667,590.625,523.333C590.625,531,590.625,538,590.625,541.5L590.625,545"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Queue_Redis_0" d="M590.625,603L590.625,607.167C590.625,611.333,590.625,619.667,590.625,628C590.625,636.333,590.625,644.667,590.625,653C590.625,661.333,590.625,669.667,525.699,681.008C460.773,692.349,330.921,706.698,265.995,713.872L201.07,721.047"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redis_Reverb_0" d="M120.047,757L120.047,761.167C120.047,765.333,120.047,773.667,120.047,782C120.047,790.333,120.047,798.667,120.047,807C120.047,815.333,120.047,823.667,351.475,835.988C582.903,848.31,1045.759,864.619,1277.188,872.774L1508.616,880.929"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Private_Comment_0" d="M1573.811,1144L1570.422,1148.167C1567.032,1152.333,1560.252,1160.667,1556.862,1169C1553.473,1177.333,1553.473,1185.667,1434.335,1196C1315.198,1206.333,1076.923,1218.667,957.786,1231C838.648,1243.333,838.648,1255.667,835.025,1265.524C831.401,1275.382,824.154,1282.764,820.531,1286.455L816.907,1290.146"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Private_Todo_0" d="M1651.838,1144L1660.489,1148.167C1669.141,1152.333,1686.443,1160.667,1695.095,1169C1703.746,1177.333,1703.746,1185.667,1588.059,1196C1472.371,1206.333,1240.996,1218.667,1125.309,1231C1009.621,1243.333,1009.621,1255.667,1009.621,1265.333C1009.621,1275,1009.621,1282,1009.621,1285.5L1009.621,1289"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Presence_Message_0" d="M1831.715,1144L1831.715,1148.167C1831.715,1152.333,1831.715,1160.667,1831.715,1169C1831.715,1177.333,1831.715,1185.667,1728.517,1196C1625.319,1206.333,1418.923,1218.667,1315.725,1231C1212.527,1243.333,1212.527,1255.667,1212.527,1265.333C1212.527,1275,1212.527,1282,1212.527,1285.5L1212.527,1289"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Channels_ChannelTypes_0" d="M1595.777,1015L1595.777,1019.167C1595.777,1023.333,1595.777,1031.667,1595.777,1039.333C1595.777,1047,1595.777,1054,1595.777,1057.5L1595.777,1061"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Reactors_Models_0" d="M701.47,320L707.316,324.167C713.162,328.333,724.854,336.667,730.701,345C736.547,353.333,736.547,361.667,736.547,370C736.547,378.333,736.547,386.667,736.547,399.5C736.547,412.333,736.547,429.667,736.547,447C736.547,464.333,736.547,481.667,736.547,494.5C736.547,507.333,736.547,515.667,736.547,528.5C736.547,541.333,736.547,558.667,736.547,576C736.547,593.333,736.547,610.667,736.547,623.5C736.547,636.333,736.547,644.667,736.547,653C736.547,661.333,736.547,669.667,736.547,682.5C736.547,695.333,736.547,712.667,736.547,730C736.547,747.333,736.547,764.667,736.547,777.5C736.547,790.333,736.547,798.667,736.547,807C736.547,815.333,736.547,823.667,736.547,836.5C736.547,849.333,736.547,866.667,736.547,884C736.547,901.333,736.547,918.667,736.547,936C736.547,953.333,736.547,970.667,736.547,988C736.547,1005.333,736.547,1022.667,736.547,1035.5C736.547,1048.333,736.547,1056.667,736.547,1069.5C736.547,1082.333,736.547,1099.667,736.547,1117C736.547,1134.333,736.547,1151.667,736.547,1164.5C736.547,1177.333,736.547,1185.667,736.547,1196C736.547,1206.333,736.547,1218.667,736.547,1230.333C736.547,1242,736.547,1253,736.547,1258.5L736.547,1264"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Models_Database_0" d="M787.598,1372L787.598,1376.167C787.598,1380.333,787.598,1388.667,692.39,1397C597.182,1405.333,406.767,1413.667,311.559,1421.333C216.352,1429,216.352,1436,216.352,1439.5L216.352,1443"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(838.6484375, 1231)" class="edgeLabel"><g transform="translate(-82.1015625, -12)" class="label"><foreignObject height="24" width="164.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>commentable.Post.{id}</p></span></div></foreignObject></g></g><g transform="translate(1009.62109375, 1231)" class="edgeLabel"><g transform="translate(-31.765625, -12)" class="label"><foreignObject height="24" width="63.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>todo.{id}</p></span></div></foreignObject></g></g><g transform="translate(1212.52734375, 1231)" class="edgeLabel"><g transform="translate(-61.125, -12)" class="label"><foreignObject height="24" width="122.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>conversation.{id}</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1498.125, 576)" id="flowchart-Browser-0" class="node default"><rect height="54" width="152.546875" y="-27" x="-76.2734375" style="" class="basic label-container"/><g transform="translate(-46.2734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="92.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Browser</p></span></div></foreignObject></g></g><g transform="translate(1693.4296875, 576)" id="flowchart-MobileApp-1" class="node default"><rect height="54" width="138.0625" y="-27" x="-69.03125" style="" class="basic label-container"/><g transform="translate(-39.03125, -12)" style="" class="label"><rect/><foreignObject height="24" width="78.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Mobile App</p></span></div></foreignObject></g></g><g transform="translate(1595.77734375, 730)" id="flowchart-Echo-2" class="node default"><rect height="54" width="198.546875" y="-27" x="-99.2734375" style="" class="basic label-container"/><g transform="translate(-69.2734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="138.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Laravel Echo Client</p></span></div></foreignObject></g></g><g transform="translate(1595.77734375, 884)" id="flowchart-Reverb-3" class="node default wsLayer"><rect height="54" width="166.328125" y="-27" x="-83.1640625" style="fill:#e8f5e9 !important;stroke:#a5d6a7 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-53.1640625, -12)" style="" class="label"><rect/><foreignObject height="24" width="106.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Laravel Reverb</p></span></div></foreignObject></g></g><g transform="translate(1595.77734375, 988)" id="flowchart-Channels-4" class="node default wsLayer"><rect height="54" width="124.125" y="-27" x="-62.0625" style="fill:#e8f5e9 !important;stroke:#a5d6a7 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-32.0625, -12)" style="" class="label"><rect/><foreignObject height="24" width="64.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Channels</p></span></div></foreignObject></g></g><g transform="translate(1595.77734375, 1117)" id="flowchart-Private-5" class="node default wsLayer"><rect height="54" width="179.21875" y="-27" x="-89.609375" style="fill:#e8f5e9 !important;stroke:#a5d6a7 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-59.609375, -12)" style="" class="label"><rect/><foreignObject height="24" width="119.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Private Channels</p></span></div></foreignObject></g></g><g transform="translate(1831.71484375, 1117)" id="flowchart-Presence-6" class="node default wsLayer"><rect height="54" width="192.65625" y="-27" x="-96.328125" style="fill:#e8f5e9 !important;stroke:#a5d6a7 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-66.328125, -12)" style="" class="label"><rect/><foreignObject height="24" width="132.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Presence Channels</p></span></div></foreignObject></g></g><g transform="translate(2064.41015625, 1117)" id="flowchart-Public-7" class="node default wsLayer"><rect height="54" width="172.734375" y="-27" x="-86.3671875" style="fill:#e8f5e9 !important;stroke:#a5d6a7 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-56.3671875, -12)" style="" class="label"><rect/><foreignObject height="24" width="112.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Public Channels</p></span></div></foreignObject></g></g><g transform="translate(711.421875, 85)" id="flowchart-Aggregates-8" class="node default appLayer"><rect height="54" width="138.4375" y="-27" x="-69.21875" style="fill:#fff3e0 !important;stroke:#ffe0b2 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-39.21875, -12)" style="" class="label"><rect/><foreignObject height="24" width="78.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Aggregates</p></span></div></foreignObject></g></g><g transform="translate(711.421875, 189)" id="flowchart-Events-9" class="node default appLayer"><rect height="54" width="164.90625" y="-27" x="-82.453125" style="fill:#fff3e0 !important;stroke:#ffe0b2 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-52.453125, -12)" style="" class="label"><rect/><foreignObject height="24" width="104.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Domain Events</p></span></div></foreignObject></g></g><g transform="translate(663.5859375, 293)" id="flowchart-Reactors-10" class="node default appLayer"><rect height="54" width="121.34375" y="-27" x="-60.671875" style="fill:#fff3e0 !important;stroke:#ffe0b2 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-30.671875, -12)" style="" class="label"><rect/><foreignObject height="24" width="61.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Reactors</p></span></div></foreignObject></g></g><g transform="translate(590.625, 447)" id="flowchart-BroadcastEvents-11" class="node default appLayer"><rect height="54" width="181.84375" y="-27" x="-90.921875" style="fill:#fff3e0 !important;stroke:#ffe0b2 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.921875, -12)" style="" class="label"><rect/><foreignObject height="24" width="121.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Broadcast Events</p></span></div></foreignObject></g></g><g transform="translate(590.625, 576)" id="flowchart-Queue-12" class="node default appLayer"><rect height="54" width="180.90625" y="-27" x="-90.453125" style="fill:#fff3e0 !important;stroke:#ffe0b2 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.453125, -12)" style="" class="label"><rect/><foreignObject height="24" width="120.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Broadcast Queue</p></span></div></foreignObject></g></g><g transform="translate(787.59765625, 1320)" id="flowchart-Comment-13" class="node default appLayer"><rect height="54" width="175.65625" y="-27" x="-87.828125" style="fill:#fff3e0 !important;stroke:#ffe0b2 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-57.828125, -12)" style="" class="label"><rect/><foreignObject height="24" width="115.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Comment Model</p></span></div></foreignObject></g></g><g transform="translate(1212.52734375, 1320)" id="flowchart-Message-14" class="node default appLayer"><rect height="54" width="165.3125" y="-27" x="-82.65625" style="fill:#fff3e0 !important;stroke:#ffe0b2 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-52.65625, -12)" style="" class="label"><rect/><foreignObject height="24" width="105.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Message Model</p></span></div></foreignObject></g></g><g transform="translate(1009.62109375, 1320)" id="flowchart-Todo-15" class="node default appLayer"><rect height="54" width="140.5" y="-27" x="-70.25" style="fill:#fff3e0 !important;stroke:#ffe0b2 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-40.25, -12)" style="" class="label"><rect/><foreignObject height="24" width="80.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Todo Model</p></span></div></foreignObject></g></g><g transform="translate(120.046875, 730)" id="flowchart-Redis-16" class="node default infraLayer"><rect height="54" width="154.09375" y="-27" x="-77.046875" style="fill:#f3e5f5 !important;stroke:#e1bee7 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-47.046875, -12)" style="" class="label"><rect/><foreignObject height="24" width="94.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis PubSub</p></span></div></foreignObject></g></g><g transform="translate(318.3984375, 730)" id="flowchart-EventStore-17" class="node default infraLayer"><rect height="54" width="142.609375" y="-27" x="-71.3046875" style="fill:#f3e5f5 !important;stroke:#e1bee7 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-41.3046875, -12)" style="" class="label"><rect/><foreignObject height="24" width="82.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Event Store</p></span></div></foreignObject></g></g><g transform="translate(216.3515625, 1474)" id="flowchart-Database-18" class="node default infraLayer"><rect height="54" width="125.5" y="-27" x="-62.75" style="fill:#f3e5f5 !important;stroke:#e1bee7 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-32.75, -12)" style="" class="label"><rect/><foreignObject height="24" width="65.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database</p></span></div></foreignObject></g></g></g></g></g></svg>