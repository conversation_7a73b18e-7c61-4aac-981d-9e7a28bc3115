# 1. Chinook Database Models Guide

## 1.1. Overview

This guide provides comprehensive instructions for creating modern Laravel 12 Eloquent models for the Chinook database schema. The Chinook database represents a digital music store with artists, albums, tracks, customers, employees, and sales data.

All models include modern Laravel 12 features:
- **Modern Casting**: Using `cast()` method instead of `$casts` property
- **Closure Table Hierarchies**: Efficient hierarchical data storage and querying
- **Timestamps**: `created_at` and `updated_at` columns
- **Soft Deletes**: Safe deletion with `deleted_at` column
- **User Stamps**: Track who created/updated records
- **Tags**: Spatie tags for categorization
- **Secondary Unique Keys**: Public-facing identifiers using `public_id`
- **Slugs**: URL-friendly identifiers generated from `public_id`

## 1.2. Database Schema Overview

The Chinook database consists of 11 interconnected tables with modern Laravel 12 enhancements:

- **Core Music Data**: `artists`, `albums`, `tracks`, `categories` (replaces genres with closure table), `media_types`
- **Customer Management**: `customers`, `employees`
- **Sales System**: `invoices`, `invoice_lines`
- **Playlist System**: `playlists`, `playlist_track`
- **Hierarchical Data**: `category_closure` (closure table for efficient hierarchical queries)

## 1.3. Required Packages

Ensure these packages are installed for full functionality:

```bash
# Core Laravel features
composer require spatie/laravel-sluggable
composer require glhd/bits

# Tags functionality
composer require spatie/laravel-tags

# User stamps (track who created/updated)
composer require wildside/userstamps

# Role-based access control (CRITICAL for enterprise features)
composer require spatie/laravel-permission

# Activity logging (optional but recommended)
composer require spatie/laravel-activitylog
```

## 1.4. Package Installation and Configuration

### 1.4.1. Spatie Laravel Permission Setup

```bash
# Install the package
composer require spatie/laravel-permission

# Publish the migration and config file
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"

# Run the migrations (creates roles, permissions, and pivot tables)
php artisan migrate

# Clear cache after installation
php artisan permission:cache-reset
```

### 1.4.2. Configuration File Customization

Update `config/permission.php` for Chinook-specific settings:

```php
<?php

return [
    'models' => [
        'permission' => Spatie\Permission\Models\Permission::class,
        'role' => Spatie\Permission\Models\Role::class,
    ],

    'table_names' => [
        'roles' => 'roles',
        'permissions' => 'permissions',
        'model_has_permissions' => 'model_has_permissions',
        'model_has_roles' => 'model_has_roles',
        'role_has_permissions' => 'role_has_permissions',
    ],

    'column_names' => [
        'role_pivot_key' => null,
        'permission_pivot_key' => null,
        'model_morph_key' => 'model_id',
        'team_foreign_key' => 'team_id',
    ],

    // Enable teams for multi-tenant Chinook instances
    'teams' => false,

    // Use cache for better performance
    'cache' => [
        'expiration_time' => \DateInterval::createFromDateString('24 hours'),
        'key' => 'spatie.permission.cache',
        'store' => 'default',
    ],
];
```

### 1.4.3. User Model Configuration

Update your `User` model to include permission traits:

```php
<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Permission\Traits\HasPermissions;

class User extends Authenticatable
{
    use HasRoles;
    use HasPermissions;

    // Your existing User model code...
}
```

## 1.5. CategoryType Enum Design

### 1.5.1. Enum Definition

Create the CategoryType enum to replace the old Genre system:

```bash
php artisan make:enum CategoryType
```

```php
<?php

declare(strict_types=1);

namespace App\Enums;

enum CategoryType: string
{
    case GENRE = 'genre';
    case MOOD = 'mood';
    case THEME = 'theme';
    case ERA = 'era';
    case INSTRUMENT = 'instrument';
    case LANGUAGE = 'language';
    case OCCASION = 'occasion';

    /**
     * Get the display label for the category type.
     */
    public function label(): string
    {
        return match($this) {
            self::GENRE => 'Music Genre',
            self::MOOD => 'Mood & Emotion',
            self::THEME => 'Theme & Style',
            self::ERA => 'Time Period',
            self::INSTRUMENT => 'Instrument Focus',
            self::LANGUAGE => 'Language',
            self::OCCASION => 'Occasion & Event',
        };
    }

    /**
     * Get the color for UI representation.
     */
    public function color(): string
    {
        return match($this) {
            self::GENRE => '#FF6B6B',      // Coral Red
            self::MOOD => '#4ECDC4',       // Turquoise
            self::THEME => '#45B7D1',      // Sky Blue
            self::ERA => '#96CEB4',        // Mint Green
            self::INSTRUMENT => '#FFEAA7', // Warm Yellow
            self::LANGUAGE => '#DDA0DD',   // Plum
            self::OCCASION => '#F8C471',   // Peach
        };
    }

    /**
     * Get the Font Awesome icon for visual identification.
     */
    public function icon(): string
    {
        return match($this) {
            self::GENRE => 'fas fa-music',
            self::MOOD => 'fas fa-heart',
            self::THEME => 'fas fa-palette',
            self::ERA => 'fas fa-clock',
            self::INSTRUMENT => 'fas fa-guitar',
            self::LANGUAGE => 'fas fa-globe',
            self::OCCASION => 'fas fa-calendar-alt',
        };
    }

    /**
     * Get validation rules for this category type.
     */
    public function validationRules(): array
    {
        return match($this) {
            self::GENRE => [
                'max_depth' => 3,
                'allowed_parents' => [self::GENRE],
                'required_fields' => ['name', 'description'],
            ],
            self::MOOD => [
                'max_depth' => 2,
                'allowed_parents' => [self::MOOD],
                'required_fields' => ['name'],
            ],
            self::THEME => [
                'max_depth' => 2,
                'allowed_parents' => [self::THEME],
                'required_fields' => ['name'],
            ],
            self::ERA => [
                'max_depth' => 2,
                'allowed_parents' => [self::ERA],
                'required_fields' => ['name', 'start_year'],
            ],
            self::INSTRUMENT => [
                'max_depth' => 3,
                'allowed_parents' => [self::INSTRUMENT],
                'required_fields' => ['name'],
            ],
            self::LANGUAGE => [
                'max_depth' => 1,
                'allowed_parents' => [],
                'required_fields' => ['name', 'iso_code'],
            ],
            self::OCCASION => [
                'max_depth' => 2,
                'allowed_parents' => [self::OCCASION],
                'required_fields' => ['name'],
            ],
        };
    }

    /**
     * Get default category suggestions for seeding.
     */
    public function defaultCategories(): array
    {
        return match($this) {
            self::GENRE => [
                'Rock' => ['Hard Rock', 'Soft Rock', 'Progressive Rock'],
                'Jazz' => ['Smooth Jazz', 'Bebop', 'Fusion'],
                'Electronic' => ['House', 'Techno', 'Ambient'],
                'Classical' => ['Baroque', 'Romantic', 'Modern'],
                'Hip-Hop' => ['East Coast', 'West Coast', 'Trap'],
            ],
            self::MOOD => [
                'Energetic' => ['High Energy', 'Motivational'],
                'Relaxing' => ['Calm', 'Peaceful'],
                'Melancholic' => ['Sad', 'Nostalgic'],
                'Upbeat' => ['Happy', 'Cheerful'],
            ],
            self::THEME => [
                'Workout' => ['Cardio', 'Strength Training'],
                'Study' => ['Focus', 'Background'],
                'Party' => ['Dance', 'Celebration'],
                'Romance' => ['Love Songs', 'Intimate'],
            ],
            self::ERA => [
                '1960s' => [],
                '1970s' => [],
                '1980s' => [],
                '1990s' => [],
                '2000s' => [],
                '2010s' => [],
                '2020s' => [],
            ],
            self::INSTRUMENT => [
                'Piano' => ['Solo Piano', 'Piano Ensemble'],
                'Guitar' => ['Acoustic Guitar', 'Electric Guitar'],
                'Orchestral' => ['Symphony', 'Chamber'],
                'Electronic' => ['Synthesizer', 'Digital'],
            ],
            self::LANGUAGE => [
                'English' => [],
                'Spanish' => [],
                'French' => [],
                'German' => [],
                'Italian' => [],
                'Japanese' => [],
                'Instrumental' => [],
            ],
            self::OCCASION => [
                'Wedding' => ['Ceremony', 'Reception'],
                'Birthday' => ['Children', 'Adult'],
                'Holiday' => ['Christmas', 'Halloween'],
                'Corporate' => ['Presentation', 'Networking'],
            ],
        };
    }

    /**
     * Get all category types as array.
     */
    public static function toArray(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    /**
     * Get category types suitable for a specific model.
     */
    public static function forModel(string $modelClass): array
    {
        return match($modelClass) {
            'App\Models\Artist' => [self::GENRE, self::ERA, self::INSTRUMENT],
            'App\Models\Album' => [self::GENRE, self::MOOD, self::THEME, self::ERA, self::LANGUAGE],
            'App\Models\Track' => [self::GENRE, self::MOOD, self::THEME, self::INSTRUMENT, self::LANGUAGE, self::OCCASION],
            'App\Models\Playlist' => [self::MOOD, self::THEME, self::OCCASION],
            'App\Models\Customer' => [self::GENRE, self::MOOD], // Preferences only
            default => self::cases(),
        };
    }
}
```

## 1.6. Model Creation Commands

### 1.6.1. Generate All Models

```bash
# Core music models (REMOVED Genre - replaced with Category)
php artisan make:model Artist
php artisan make:model Album
php artisan make:model Track
php artisan make:model Category  # NEW: Replaces Genre with polymorphic system
php artisan make:model MediaType

# Customer and employee models
php artisan make:model Customer
php artisan make:model Employee

# Sales models
php artisan make:model Invoice
php artisan make:model InvoiceLine

# Playlist models
php artisan make:model Playlist

# Create the CategoryType enum
php artisan make:enum CategoryType
```

## 1.7. Model Implementations

### 1.7.1. Category Model (Replaces Genre - Core Polymorphic System with Closure Table)

```php
<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\CategoryType;
use App\Enums\SecondaryKeyType;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Wildside\Userstamps\Userstamps;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

class Category extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;
    use HasRoles;
    use HasPermissions;

    /**
     * The table associated with the model.
     */
    protected $table = 'categories';

    /**
     * Get the secondary key type for this model.
     * Using UUID for categories - standards compliance for reference data.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::UUID;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'type',
        'sort_order',
        'is_active',
        'color',
        'icon',
        'metadata',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'type' => CategoryType::class,
            'is_active' => 'boolean',
            'metadata' => 'array',
            'sort_order' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the parent categories through closure table.
     */
    public function parents(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_closure', 'descendant_id', 'ancestor_id')
            ->wherePivot('depth', 1)
            ->orderBy('sort_order');
    }

    /**
     * Get the direct parent category.
     */
    public function parent(): ?Category
    {
        return $this->parents()->first();
    }

    /**
     * Get the child categories through closure table.
     */
    public function children(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_closure', 'ancestor_id', 'descendant_id')
            ->wherePivot('depth', 1)
            ->orderBy('sort_order');
    }

    /**
     * Get all descendants through closure table.
     */
    public function descendants(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_closure', 'ancestor_id', 'descendant_id')
            ->wherePivot('depth', '>', 0)
            ->orderBy('category_closure.depth')
            ->orderBy('sort_order');
    }

    /**
     * Get all ancestors through closure table.
     */
    public function ancestors(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_closure', 'descendant_id', 'ancestor_id')
            ->wherePivot('depth', '>', 0)
            ->orderBy('category_closure.depth', 'desc');
    }

    /**
     * Get siblings (categories with the same parent).
     */
    public function siblings(): Collection
    {
        $parent = $this->parent();
        if (!$parent) {
            return Category::whereDoesntHave('parents')->where('id', '!=', $this->id)->get();
        }

        return $parent->children()->where('id', '!=', $this->id)->get();
    }

    /**
     * Get the root category of this tree.
     */
    public function root(): ?Category
    {
        $ancestors = $this->ancestors()->get();
        return $ancestors->isEmpty() ? $this : $ancestors->last();
    }

    /**
     * Get leaf categories (no children).
     */
    public function scopeLeaves(Builder $query): Builder
    {
        return $query->whereDoesntHave('children');
    }

    /**
     * Get root categories (no parents).
     */
    public function scopeRoots(Builder $query): Builder
    {
        return $query->whereDoesntHave('parents');
    }

    /**
     * Scope by category type.
     */
    public function scopeOfType(Builder $query, CategoryType $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope by active status.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by depth level in the hierarchy.
     */
    public function scopeAtDepth(Builder $query, int $depth): Builder
    {
        return $query->whereHas('ancestors', function ($q) use ($depth) {
            $q->havingRaw('COUNT(*) = ?', [$depth]);
        });
    }

    /**
     * Polymorphic relationship to artists.
     */
    public function artists(): MorphToMany
    {
        return $this->morphedByMany(Artist::class, 'categorizable');
    }

    /**
     * Polymorphic relationship to albums.
     */
    public function albums(): MorphToMany
    {
        return $this->morphedByMany(Album::class, 'categorizable');
    }

    /**
     * Polymorphic relationship to tracks.
     */
    public function tracks(): MorphToMany
    {
        return $this->morphedByMany(Track::class, 'categorizable');
    }

    /**
     * Polymorphic relationship to playlists.
     */
    public function playlists(): MorphToMany
    {
        return $this->morphedByMany(Playlist::class, 'categorizable');
    }

    /**
     * Polymorphic relationship to customers.
     */
    public function customers(): MorphToMany
    {
        return $this->morphedByMany(Customer::class, 'categorizable');
    }

    /**
     * Make this category a child of another category using closure table.
     */
    public function makeChildOf(Category $parent): bool
    {
        if ($this->wouldCreateCircularReference($parent)) {
            return false;
        }

        // Remove existing parent relationships
        $this->removeFromHierarchy();

        // Add direct parent relationship
        $this->parents()->attach($parent->id, ['depth' => 1]);

        // Add all ancestor relationships
        $parentAncestors = $parent->ancestors()->get();
        foreach ($parentAncestors as $ancestor) {
            $depth = $ancestor->pivot->depth + 1;
            $this->parents()->attach($ancestor->id, ['depth' => $depth]);
        }

        // Update all descendants to include new ancestors
        $this->updateDescendantAncestors();

        return true;
    }

    /**
     * Make this category a root category.
     */
    public function makeRoot(): bool
    {
        $this->removeFromHierarchy();
        return true;
    }

    /**
     * Remove this category from the hierarchy (closure table).
     */
    protected function removeFromHierarchy(): void
    {
        // Remove all ancestor relationships for this category
        $this->parents()->detach();

        // Remove all descendant relationships where this is an ancestor
        $this->descendants()->detach();
    }

    /**
     * Update descendant ancestors after hierarchy change.
     */
    protected function updateDescendantAncestors(): void
    {
        $descendants = $this->descendants()->get();
        $ancestors = $this->ancestors()->get();

        foreach ($descendants as $descendant) {
            foreach ($ancestors as $ancestor) {
                $newDepth = $ancestor->pivot->depth + $descendant->pivot->depth;
                $descendant->parents()->syncWithoutDetaching([
                    $ancestor->id => ['depth' => $newDepth]
                ]);
            }
        }
    }

    /**
     * Check if making this category a child would create a circular reference.
     */
    protected function wouldCreateCircularReference(Category $potentialParent): bool
    {
        if ($potentialParent->id === $this->id) {
            return true;
        }

        return $this->descendants()->pluck('id')->contains($potentialParent->id);
    }

    /**
     * Get the full hierarchical name.
     */
    public function getFullNameAttribute(): string
    {
        $ancestors = $this->ancestors()->get();
        $names = $ancestors->pluck('name')->push($this->name);

        return $names->implode(' > ');
    }

    /**
     * Get the category type label.
     */
    public function getTypeLabelAttribute(): string
    {
        return $this->type->label();
    }

    /**
     * Get the category type color.
     */
    public function getTypeColorAttribute(): string
    {
        return $this->type->color();
    }

    /**
     * Get the category type icon.
     */
    public function getTypeIconAttribute(): string
    {
        return $this->type->icon();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($category) {
            // Add self-reference with depth 0 for closure table
            $category->parents()->attach($category->id, ['depth' => 0]);
        });

        static::deleting(function ($category) {
            // Clean up closure table relationships
            $category->removeFromHierarchy();
        });
    }
}
```

### 1.7.2. Artist Model (Updated with Polymorphic Categories and RBAC)

```php
<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\CategoryType;
use App\Enums\SecondaryKeyType;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Wildside\Userstamps\Userstamps;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

class Artist extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;
    use HasRoles;
    use HasPermissions;

    /**
     * The table associated with the model.
     */
    protected $table = 'artists';

    /**
     * Get the secondary key type for this model.
     * Using ULID for artists - good balance of readability and performance.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::ULID;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'biography',
        'website',
        'social_links',
        'country',
        'formed_year',
        'is_active',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'social_links' => 'array',
            'formed_year' => 'integer',
            'is_active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the albums for the artist.
     */
    public function albums(): HasMany
    {
        return $this->hasMany(Album::class);
    }

    /**
     * Get all tracks for this artist through albums.
     */
    public function tracks(): HasMany
    {
        return $this->hasManyThrough(Track::class, Album::class);
    }

    /**
     * Polymorphic relationship to categories.
     */
    public function categories(): MorphToMany
    {
        return $this->morphToMany(Category::class, 'categorizable')
            ->withTimestamps()
            ->withPivot(['created_by', 'updated_by']);
    }

    /**
     * Get genres (categories of type GENRE).
     */
    public function genres(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::GENRE);
    }

    /**
     * Get eras (categories of type ERA).
     */
    public function eras(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::ERA);
    }

    /**
     * Get instruments (categories of type INSTRUMENT).
     */
    public function instruments(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::INSTRUMENT);
    }

    /**
     * Scope to find artists by genre.
     */
    public function scopeByGenre(Builder $query, string $genreName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($genreName) {
            $q->where('type', CategoryType::GENRE)
              ->where('name', 'like', "%{$genreName}%");
        });
    }

    /**
     * Scope to find artists by era.
     */
    public function scopeByEra(Builder $query, string $eraName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($eraName) {
            $q->where('type', CategoryType::ERA)
              ->where('name', 'like', "%{$eraName}%");
        });
    }

    /**
     * Scope to find published artists (those with albums).
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->whereHas('albums');
    }

    /**
     * Scope to find active artists.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Attach a category to this artist.
     */
    public function attachCategory(Category $category, ?int $userId = null): void
    {
        $this->categories()->attach($category->id, [
            'created_by' => $userId ?? auth()->id(),
            'updated_by' => $userId ?? auth()->id(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Sync categories by type.
     */
    public function syncCategoriesByType(CategoryType $type, array $categoryIds): void
    {
        $existingOtherTypes = $this->categories()
            ->where('type', '!=', $type)
            ->pluck('categories.id')
            ->toArray();

        $this->categories()->sync(array_merge($existingOtherTypes, $categoryIds));
    }

    /**
     * Get categories grouped by type.
     */
    public function getCategoriesByType(): array
    {
        return $this->categories()
            ->get()
            ->groupBy('type')
            ->map(function ($categories) {
                return $categories->pluck('name', 'id');
            })
            ->toArray();
    }

    /**
     * Get the artist's display name with album count.
     */
    public function getDisplayNameAttribute(): string
    {
        $albumCount = $this->albums()->count();
        return "{$this->name} ({$albumCount} albums)";
    }

    /**
     * Get the primary genre for this artist.
     */
    public function getPrimaryGenreAttribute(): ?Category
    {
        return $this->genres()->first();
    }

    /**
     * Get the artist's years active.
     */
    public function getYearsActiveAttribute(): string
    {
        if (!$this->formed_year) {
            return 'Unknown';
        }

        $endYear = $this->is_active ? 'Present' : 'Unknown';
        return "{$this->formed_year} - {$endYear}";
    }
}
```

### 1.7.3. Album Model (Updated with Polymorphic Categories and RBAC)

```php
<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\CategoryType;
use App\Enums\SecondaryKeyType;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Wildside\Userstamps\Userstamps;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

class Album extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;
    use HasRoles;
    use HasPermissions;

    /**
     * The table associated with the model.
     */
    protected $table = 'albums';

    /**
     * Get the secondary key type for this model.
     * Using ULID for albums - good for chronological ordering and URL-friendly.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::ULID;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'artist_id',
        'release_date',
        'label',
        'catalog_number',
        'description',
        'cover_image_url',
        'total_tracks',
        'total_duration_ms',
        'is_compilation',
        'is_explicit',
        'is_active',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'release_date' => 'date',
            'total_tracks' => 'integer',
            'total_duration_ms' => 'integer',
            'is_compilation' => 'boolean',
            'is_explicit' => 'boolean',
            'is_active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the artist that owns the album.
     */
    public function artist(): BelongsTo
    {
        return $this->belongsTo(Artist::class);
    }

    /**
     * Get the tracks for the album.
     */
    public function tracks(): HasMany
    {
        return $this->hasMany(Track::class);
    }

    /**
     * Polymorphic relationship to categories.
     */
    public function categories(): MorphToMany
    {
        return $this->morphToMany(Category::class, 'categorizable')
            ->withTimestamps()
            ->withPivot(['created_by', 'updated_by']);
    }

    /**
     * Get genres (categories of type GENRE).
     */
    public function genres(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::GENRE);
    }

    /**
     * Get moods (categories of type MOOD).
     */
    public function moods(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::MOOD);
    }

    /**
     * Get themes (categories of type THEME).
     */
    public function themes(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::THEME);
    }

    /**
     * Get eras (categories of type ERA).
     */
    public function eras(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::ERA);
    }

    /**
     * Get languages (categories of type LANGUAGE).
     */
    public function languages(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::LANGUAGE);
    }

    /**
     * Scope to find albums by genre.
     */
    public function scopeByGenre(Builder $query, string $genreName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($genreName) {
            $q->where('type', CategoryType::GENRE)
              ->where('name', 'like', "%{$genreName}%");
        });
    }

    /**
     * Scope to find albums by mood.
     */
    public function scopeByMood(Builder $query, string $moodName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($moodName) {
            $q->where('type', CategoryType::MOOD)
              ->where('name', 'like', "%{$moodName}%");
        });
    }

    /**
     * Scope to find albums by era.
     */
    public function scopeByEra(Builder $query, string $eraName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($eraName) {
            $q->where('type', CategoryType::ERA)
              ->where('name', 'like', "%{$eraName}%");
        });
    }

    /**
     * Scope to find albums with tracks.
     */
    public function scopeWithTracks(Builder $query): Builder
    {
        return $query->whereHas('tracks');
    }

    /**
     * Scope to find active albums.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Sync categories by type.
     */
    public function syncCategoriesByType(CategoryType $type, array $categoryIds): void
    {
        $existingOtherTypes = $this->categories()
            ->where('type', '!=', $type)
            ->pluck('categories.id')
            ->toArray();

        $this->categories()->sync(array_merge($existingOtherTypes, $categoryIds));
    }

    /**
     * Get the album's full title with artist name.
     */
    public function getFullTitleAttribute(): string
    {
        return "{$this->artist->name} - {$this->title}";
    }

    /**
     * Get the total duration of all tracks in the album.
     */
    public function getTotalDurationAttribute(): int
    {
        return $this->tracks()->sum('milliseconds');
    }

    /**
     * Get the primary genre for this album.
     */
    public function getPrimaryGenreAttribute(): ?Category
    {
        return $this->genres()->first();
    }

    /**
     * Get the formatted duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        $totalSeconds = intval($this->total_duration / 1000);
        $hours = intval($totalSeconds / 3600);
        $minutes = intval(($totalSeconds % 3600) / 60);
        $seconds = $totalSeconds % 60;

        if ($hours > 0) {
            return sprintf('%d:%02d:%02d', $hours, $minutes, $seconds);
        }

        return sprintf('%d:%02d', $minutes, $seconds);
    }
}
```

### 1.7.4. Track Model (Updated with Polymorphic Categories and RBAC)

```php
<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\CategoryType;
use App\Enums\SecondaryKeyType;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Wildside\Userstamps\Userstamps;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

class Track extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;
    use HasRoles;
    use HasPermissions;

    /**
     * The table associated with the model.
     */
    protected $table = 'tracks';

    /**
     * Get the secondary key type for this model.
     * Using Snowflake for tracks - high performance for large datasets.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::SNOWFLAKE;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'album_id',
        'media_type_id',
        'composer',
        'milliseconds',
        'bytes',
        'unit_price',
        'track_number',
        'disc_number',
        'is_explicit',
        'is_active',
        'preview_url',
        'lyrics',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'unit_price' => 'decimal:2',
            'milliseconds' => 'integer',
            'bytes' => 'integer',
            'track_number' => 'integer',
            'disc_number' => 'integer',
            'is_explicit' => 'boolean',
            'is_active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the album that owns the track.
     */
    public function album(): BelongsTo
    {
        return $this->belongsTo(Album::class);
    }

    /**
     * Get the media type that owns the track.
     */
    public function mediaType(): BelongsTo
    {
        return $this->belongsTo(MediaType::class);
    }

    /**
     * Get the artist through the album relationship.
     */
    public function artist(): BelongsTo
    {
        return $this->album()->artist();
    }

    /**
     * Get the invoice lines for the track.
     */
    public function invoiceLines(): HasMany
    {
        return $this->hasMany(InvoiceLine::class);
    }

    /**
     * Get the playlists that contain this track.
     */
    public function playlists(): BelongsToMany
    {
        return $this->belongsToMany(Playlist::class, 'playlist_track')
            ->withTimestamps()
            ->withPivot(['position', 'added_by']);
    }

    /**
     * Polymorphic relationship to categories.
     */
    public function categories(): MorphToMany
    {
        return $this->morphToMany(Category::class, 'categorizable')
            ->withTimestamps()
            ->withPivot(['created_by', 'updated_by']);
    }

    /**
     * Get genres (categories of type GENRE).
     */
    public function genres(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::GENRE);
    }

    /**
     * Get moods (categories of type MOOD).
     */
    public function moods(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::MOOD);
    }

    /**
     * Get themes (categories of type THEME).
     */
    public function themes(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::THEME);
    }

    /**
     * Get instruments (categories of type INSTRUMENT).
     */
    public function instruments(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::INSTRUMENT);
    }

    /**
     * Get languages (categories of type LANGUAGE).
     */
    public function languages(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::LANGUAGE);
    }

    /**
     * Get occasions (categories of type OCCASION).
     */
    public function occasions(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::OCCASION);
    }

    /**
     * Scope to find tracks by genre.
     */
    public function scopeByGenre(Builder $query, string $genreName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($genreName) {
            $q->where('type', CategoryType::GENRE)
              ->where('name', 'like', "%{$genreName}%");
        });
    }

    /**
     * Scope to find tracks by mood.
     */
    public function scopeByMood(Builder $query, string $moodName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($moodName) {
            $q->where('type', CategoryType::MOOD)
              ->where('name', 'like', "%{$moodName}%");
        });
    }

    /**
     * Scope to find tracks by theme.
     */
    public function scopeByTheme(Builder $query, string $themeName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($themeName) {
            $q->where('type', CategoryType::THEME)
              ->where('name', 'like', "%{$themeName}%");
        });
    }

    /**
     * Scope to find tracks by instrument.
     */
    public function scopeByInstrument(Builder $query, string $instrumentName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($instrumentName) {
            $q->where('type', CategoryType::INSTRUMENT)
              ->where('name', 'like', "%{$instrumentName}%");
        });
    }

    /**
     * Scope to find tracks by language.
     */
    public function scopeByLanguage(Builder $query, string $languageName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($languageName) {
            $q->where('type', CategoryType::LANGUAGE)
              ->where('name', 'like', "%{$languageName}%");
        });
    }

    /**
     * Scope to find tracks by occasion.
     */
    public function scopeByOccasion(Builder $query, string $occasionName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($occasionName) {
            $q->where('type', CategoryType::OCCASION)
              ->where('name', 'like', "%{$occasionName}%");
        });
    }

    /**
     * Scope to find tracks by duration range.
     */
    public function scopeByDuration(Builder $query, int $minMs, int $maxMs): Builder
    {
        return $query->whereBetween('milliseconds', [$minMs, $maxMs]);
    }

    /**
     * Scope to find popular tracks (frequently purchased).
     */
    public function scopePopular(Builder $query, int $minPurchases = 10): Builder
    {
        return $query->whereHas('invoiceLines', function ($q) use ($minPurchases) {
            $q->havingRaw('COUNT(*) >= ?', [$minPurchases]);
        });
    }

    /**
     * Scope to find active tracks.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Sync categories by type.
     */
    public function syncCategoriesByType(CategoryType $type, array $categoryIds): void
    {
        $existingOtherTypes = $this->categories()
            ->where('type', '!=', $type)
            ->pluck('categories.id')
            ->toArray();

        $this->categories()->sync(array_merge($existingOtherTypes, $categoryIds));
    }

    /**
     * Get the track's duration in human-readable format.
     */
    public function getDurationAttribute(): string
    {
        $seconds = intval($this->milliseconds / 1000);
        $minutes = intval($seconds / 60);
        $remainingSeconds = $seconds % 60;

        return sprintf('%d:%02d', $minutes, $remainingSeconds);
    }

    /**
     * Get the track's file size in human-readable format.
     */
    public function getFileSizeAttribute(): string
    {
        if (!$this->bytes) {
            return 'Unknown';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = $this->bytes;
        $i = 0;

        while ($bytes >= 1024 && $i < count($units) - 1) {
            $bytes /= 1024;
            $i++;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the primary genre for this track.
     */
    public function getPrimaryGenreAttribute(): ?Category
    {
        return $this->genres()->first();
    }

    /**
     * Get the track's full name with artist and album.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->artist->name} - {$this->album->title} - {$this->name}";
    }
}
```

### 1.7.5. Playlist Model (Updated with Polymorphic Categories and RBAC)

```php
<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\CategoryType;
use App\Enums\SecondaryKeyType;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Wildside\Userstamps\Userstamps;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

class Playlist extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;
    use HasRoles;
    use HasPermissions;

    /**
     * The table associated with the model.
     */
    protected $table = 'playlists';

    /**
     * Get the secondary key type for this model.
     * Using ULID for playlists - user-friendly for sharing.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::ULID;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'is_public',
        'is_collaborative',
        'cover_image_url',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'is_public' => 'boolean',
            'is_collaborative' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the tracks that belong to this playlist.
     */
    public function tracks(): BelongsToMany
    {
        return $this->belongsToMany(Track::class, 'playlist_track')
            ->withTimestamps()
            ->withPivot(['position', 'added_by'])
            ->orderBy('pivot_position');
    }

    /**
     * Polymorphic relationship to categories.
     */
    public function categories(): MorphToMany
    {
        return $this->morphToMany(Category::class, 'categorizable')
            ->withTimestamps()
            ->withPivot(['created_by', 'updated_by']);
    }

    /**
     * Get moods (categories of type MOOD).
     */
    public function moods(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::MOOD);
    }

    /**
     * Get themes (categories of type THEME).
     */
    public function themes(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::THEME);
    }

    /**
     * Get occasions (categories of type OCCASION).
     */
    public function occasions(): MorphToMany
    {
        return $this->categories()->where('type', CategoryType::OCCASION);
    }

    /**
     * Scope to find public playlists.
     */
    public function scopePublic(Builder $query): Builder
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope to find playlists with tracks.
     */
    public function scopeWithTracks(Builder $query): Builder
    {
        return $query->whereHas('tracks');
    }

    /**
     * Scope to find playlists by mood.
     */
    public function scopeByMood(Builder $query, string $moodName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($moodName) {
            $q->where('type', CategoryType::MOOD)
              ->where('name', 'like', "%{$moodName}%");
        });
    }

    /**
     * Scope to find playlists by theme.
     */
    public function scopeByTheme(Builder $query, string $themeName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($themeName) {
            $q->where('type', CategoryType::THEME)
              ->where('name', 'like', "%{$themeName}%");
        });
    }

    /**
     * Scope to find playlists by occasion.
     */
    public function scopeByOccasion(Builder $query, string $occasionName): Builder
    {
        return $query->whereHas('categories', function ($q) use ($occasionName) {
            $q->where('type', CategoryType::OCCASION)
              ->where('name', 'like', "%{$occasionName}%");
        });
    }

    /**
     * Add a track to the playlist.
     */
    public function addTrack(Track $track, ?int $position = null, ?int $addedBy = null): void
    {
        $position = $position ?? ($this->tracks()->max('pivot_position') + 1);

        $this->tracks()->attach($track->id, [
            'position' => $position,
            'added_by' => $addedBy ?? auth()->id(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Get the playlist's track count.
     */
    public function getTrackCountAttribute(): int
    {
        return $this->tracks()->count();
    }

    /**
     * Get the playlist's total duration.
     */
    public function getTotalDurationAttribute(): int
    {
        return $this->tracks()->sum('milliseconds');
    }

    /**
     * Get the playlist's formatted duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        $totalSeconds = intval($this->total_duration / 1000);
        $hours = intval($totalSeconds / 3600);
        $minutes = intval(($totalSeconds % 3600) / 60);
        $seconds = $totalSeconds % 60;

        if ($hours > 0) {
            return sprintf('%d:%02d:%02d', $hours, $minutes, $seconds);
        }

        return sprintf('%d:%02d', $minutes, $seconds);
    }
}
```

### 1.5.6. Customer Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\SecondaryKeyType;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

class Customer extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;

    /**
     * The table associated with the model.
     */
    protected $table = 'customers';

    /**
     * Get the secondary key type for this model.
     * Using ULID for customers - good balance for customer management.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::ULID;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'company',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'phone',
        'fax',
        'email',
        'support_rep_id',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the support representative for the customer.
     */
    public function supportRep(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'support_rep_id');
    }

    /**
     * Get the invoices for the customer.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Scope to find customers by country.
     */
    public function scopeByCountry($query, string $country)
    {
        return $query->where('country', $country);
    }

    /**
     * Scope to find customers with recent purchases.
     */
    public function scopeRecentCustomers($query, int $days = 30)
    {
        return $query->whereHas('invoices', function ($q) use ($days) {
            $q->where('invoice_date', '>=', now()->subDays($days));
        });
    }

    /**
     * Get the customer's full name.
     */
    public function getFullNameAttribute(): string
    {
        return trim("{$this->first_name} {$this->last_name}");
    }

    /**
     * Get the customer's total spent amount.
     */
    public function getTotalSpentAttribute(): float
    {
        return $this->invoices()->sum('total');
    }

    /**
     * Get the customer's invoice count.
     */
    public function getInvoiceCountAttribute(): int
    {
        return $this->invoices()->count();
    }
}
```

## 1.6. Key Model Features

### 1.6.1. Modern Laravel Features

All Chinook models now include:
- **Timestamps**: Full `created_at` and `updated_at` support
- **Soft Deletes**: Safe deletion with `deleted_at` column
- **User Stamps**: Track who created/updated records with `created_by` and `updated_by`
- **Tags**: Spatie tags for flexible categorization
- **Secondary Unique Keys**: Public-facing identifiers using configurable types
- **Slugs**: URL-friendly identifiers generated from `public_id`

### 1.6.2. Secondary Key Type Strategy

Each model uses an appropriate secondary key type:
- **Artists, Albums, Customers**: ULID (balanced performance and readability)
- **Tracks**: Snowflake (high performance for large datasets)
- **Genres, MediaTypes**: UUID (standards compliance for reference data)
- **Employees, Invoices**: ULID (good for business records)
- **Playlists**: ULID (user-friendly for playlist sharing)

### 1.6.3. Fillable Attributes

Each model defines comprehensive `$fillable` arrays including:
- Original Chinook fields
- Modern Laravel fields (`public_id`, `slug`)
- Audit trail support

### 1.6.4. Type Casting

Models use enhanced `$casts` arrays for:
- Decimal prices with proper precision
- DateTime fields for timestamps
- Integer values for counts and durations
- Boolean flags where applicable

### 1.6.5. Relationship Methods

All models include comprehensive relationship methods:
- `belongsTo()` for foreign key relationships
- `hasMany()` for one-to-many relationships
- `hasManyThrough()` for indirect relationships
- `belongsToMany()` for many-to-many relationships

### 1.5.7. Employee Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\SecondaryKeyType;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

class Employee extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;

    /**
     * The table associated with the model.
     */
    protected $table = 'employees';

    /**
     * Get the secondary key type for this model.
     * Using ULID for employees - good for HR management.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::ULID;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'last_name',
        'first_name',
        'title',
        'reports_to',
        'birth_date',
        'hire_date',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'phone',
        'fax',
        'email',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'birth_date' => 'date',
            'hire_date' => 'date',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the manager that this employee reports to.
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'reports_to');
    }

    /**
     * Get the employees that report to this employee.
     */
    public function subordinates(): HasMany
    {
        return $this->hasMany(Employee::class, 'reports_to');
    }

    /**
     * Get the customers assigned to this employee.
     */
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class, 'support_rep_id');
    }

    /**
     * Scope to find managers (employees with subordinates).
     */
    public function scopeManagers($query)
    {
        return $query->whereHas('subordinates');
    }

    /**
     * Scope to find employees by title.
     */
    public function scopeByTitle($query, string $title)
    {
        return $query->where('title', 'like', "%{$title}%");
    }

    /**
     * Get the employee's full name.
     */
    public function getFullNameAttribute(): string
    {
        return trim("{$this->first_name} {$this->last_name}");
    }

    /**
     * Get the employee's years of service.
     */
    public function getYearsOfServiceAttribute(): int
    {
        return $this->hire_date ? $this->hire_date->diffInYears(now()) : 0;
    }

    /**
     * Get the employee's customer count.
     */
    public function getCustomerCountAttribute(): int
    {
        return $this->customers()->count();
    }

    /**
     * Check if this employee is a manager.
     */
    public function getIsManagerAttribute(): bool
    {
        return $this->subordinates()->exists();
    }
}
```

### 1.5.8. Invoice Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\SecondaryKeyType;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

class Invoice extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;

    /**
     * The table associated with the model.
     */
    protected $table = 'invoices';

    /**
     * Get the secondary key type for this model.
     * Using ULID for invoices - good for business records and chronological ordering.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::ULID;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'customer_id',
        'invoice_date',
        'billing_address',
        'billing_city',
        'billing_state',
        'billing_country',
        'billing_postal_code',
        'total',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'invoice_date' => 'datetime',
            'total' => 'decimal:2',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the customer that owns the invoice.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the invoice lines for the invoice.
     */
    public function invoiceLines(): HasMany
    {
        return $this->hasMany(InvoiceLine::class);
    }

    /**
     * Scope to find invoices by date range.
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('invoice_date', [$startDate, $endDate]);
    }

    /**
     * Scope to find invoices above a certain amount.
     */
    public function scopeAboveAmount($query, float $amount)
    {
        return $query->where('total', '>', $amount);
    }

    /**
     * Get the invoice's line count.
     */
    public function getLineCountAttribute(): int
    {
        return $this->invoiceLines()->count();
    }

    /**
     * Get the invoice's formatted total.
     */
    public function getFormattedTotalAttribute(): string
    {
        return '$' . number_format($this->total, 2);
    }

    /**
     * Calculate the invoice total from line items.
     */
    public function calculateTotal(): float
    {
        return $this->invoiceLines()->sum(\DB::raw('unit_price * quantity'));
    }
}
```

### 1.5.9. InvoiceLine Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\SecondaryKeyType;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

class InvoiceLine extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;

    /**
     * The table associated with the model.
     */
    protected $table = 'invoice_lines';

    /**
     * Get the secondary key type for this model.
     * Using Snowflake for invoice lines - high performance for transaction data.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::SNOWFLAKE;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'invoice_id',
        'track_id',
        'unit_price',
        'quantity',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'unit_price' => 'decimal:2',
            'quantity' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the invoice that owns the invoice line.
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the track that owns the invoice line.
     */
    public function track(): BelongsTo
    {
        return $this->belongsTo(Track::class);
    }

    /**
     * Calculate the line total (unit_price * quantity).
     */
    public function getLineTotalAttribute(): float
    {
        return $this->unit_price * $this->quantity;
    }

    /**
     * Get the formatted line total.
     */
    public function getFormattedLineTotalAttribute(): string
    {
        return '$' . number_format($this->line_total, 2);
    }

    /**
     * Scope to find lines above a certain amount.
     */
    public function scopeAboveAmount($query, float $amount)
    {
        return $query->whereRaw('unit_price * quantity > ?', [$amount]);
    }
}
```

### 1.5.10. Playlist Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\SecondaryKeyType;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;

class Playlist extends Model
{
    use HasFactory;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;

    /**
     * The table associated with the model.
     */
    protected $table = 'playlists';

    /**
     * Get the secondary key type for this model.
     * Using ULID for playlists - user-friendly for sharing.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::ULID;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'is_public',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'is_public' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the tracks that belong to this playlist.
     */
    public function tracks(): BelongsToMany
    {
        return $this->belongsToMany(Track::class, 'playlist_track')
            ->withTimestamps()
            ->withPivot(['position', 'added_by']);
    }

    /**
     * Scope to find public playlists.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope to find playlists with tracks.
     */
    public function scopeWithTracks($query)
    {
        return $query->whereHas('tracks');
    }

    /**
     * Get the playlist's track count.
     */
    public function getTrackCountAttribute(): int
    {
        return $this->tracks()->count();
    }

    /**
     * Get the playlist's total duration.
     */
    public function getTotalDurationAttribute(): int
    {
        return $this->tracks()->sum('milliseconds');
    }

    /**
     * Get the playlist's formatted duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        $totalSeconds = intval($this->total_duration / 1000);
        $hours = intval($totalSeconds / 3600);
        $minutes = intval(($totalSeconds % 3600) / 60);
        $seconds = $totalSeconds % 60;

        if ($hours > 0) {
            return sprintf('%d:%02d:%02d', $hours, $minutes, $seconds);
        }

        return sprintf('%d:%02d', $minutes, $seconds);
    }
}
```

## 1.8. Role-Based Access Control Integration

### 1.8.1. Permission System Overview

The Chinook system implements a comprehensive role-based access control system with these key roles:

**Role Hierarchy:**
1. **Super Admin**: Complete system control
2. **Admin**: Full business operations
3. **Manager**: Department management
4. **Editor**: Content management
5. **Customer Service**: Customer support
6. **User**: Standard customer
7. **Guest**: Public access

### 1.8.2. Model-Level Authorization Examples

```php
// Check permissions in controllers
class ArtistController extends Controller
{
    public function index()
    {
        $this->authorize('view-artists');
        return Artist::with('categories')->get();
    }

    public function store(Request $request)
    {
        $this->authorize('create-artists');

        $artist = Artist::create($request->validated());
        $artist->syncCategoriesByType(CategoryType::GENRE, $request->genre_ids);

        return $artist;
    }

    public function update(Request $request, Artist $artist)
    {
        $this->authorize('edit-artists');

        $artist->update($request->validated());

        return $artist;
    }
}
```

### 1.8.3. Blade Template Authorization

```blade
@can('view-artists')
    <a href="{{ route('artists.index') }}">View Artists</a>
@endcan

@role('admin|manager')
    <a href="{{ route('artists.create') }}">Create Artist</a>
@endrole

@hasrole('super-admin')
    <a href="{{ route('admin.users') }}">Manage Users</a>
@endhasrole

@cannot('edit-artists')
    <p>You don't have permission to edit artists.</p>
@endcannot
```

## 1.9. Polymorphic Category Usage Examples

### 1.9.1. Category Assignment and Retrieval

```php
// Create categories
$rockGenre = Category::create([
    'name' => 'Rock',
    'type' => CategoryType::GENRE,
    'description' => 'Rock music genre',
]);

$energeticMood = Category::create([
    'name' => 'Energetic',
    'type' => CategoryType::MOOD,
    'description' => 'High energy music',
]);

// Assign categories to models
$artist = Artist::first();
$artist->categories()->attach([$rockGenre->id, $energeticMood->id]);

// Or use helper methods
$album = Album::first();
$album->syncCategoriesByType(CategoryType::GENRE, [$rockGenre->id]);
$album->syncCategoriesByType(CategoryType::MOOD, [$energeticMood->id]);

// Retrieve by category type
$rockArtists = Artist::byGenre('Rock')->get();
$energeticTracks = Track::byMood('Energetic')->get();
$workoutPlaylists = Playlist::byTheme('Workout')->get();
```

### 1.9.2. Hierarchical Category Management

```php
// Create category hierarchy
$rock = Category::create(['name' => 'Rock', 'type' => CategoryType::GENRE]);
$hardRock = Category::create(['name' => 'Hard Rock', 'type' => CategoryType::GENRE]);
$hardRock->makeChildOf($rock);

$heavyMetal = Category::create(['name' => 'Heavy Metal', 'type' => CategoryType::GENRE]);
$heavyMetal->makeChildOf($hardRock);

// Navigate hierarchy
$ancestors = $heavyMetal->ancestors(); // [Rock, Hard Rock]
$descendants = $rock->descendants(); // [Hard Rock, Heavy Metal]
$siblings = $hardRock->siblings(); // Other children of Rock

// Get full hierarchical name
echo $heavyMetal->full_name; // "Rock > Hard Rock > Heavy Metal"
```

### 1.9.3. Advanced Category Queries

```php
// Find tracks with multiple category types
$tracks = Track::whereHas('categories', function ($q) {
    $q->where('type', CategoryType::GENRE)->where('name', 'Rock');
})->whereHas('categories', function ($q) {
    $q->where('type', CategoryType::MOOD)->where('name', 'Energetic');
})->get();

// Get category statistics
$genreStats = Category::ofType(CategoryType::GENRE)
    ->withCount(['tracks', 'albums', 'artists'])
    ->get();

// Find popular categories
$popularGenres = Category::ofType(CategoryType::GENRE)
    ->whereHas('tracks', function ($q) {
        $q->popular(50); // Tracks with 50+ purchases
    })->get();
```

## 1.10. Model Usage Examples with Modern Features

### 1.10.1. Basic Queries with Categories

```php
// Get all artists with their categories
$artists = Artist::with('categories')->get();

// Find artist by slug (URL-friendly)
$artist = Artist::where('slug', 'artist-slug')->first();

// Find artist by public_id (API-friendly)
$artist = Artist::findBySecondaryKey('01ARZ3NDEKTSV4RRFFQ69G5FAV');

// Get tracks with categories and album information
$tracks = Track::with(['album.artist', 'categories', 'mediaType'])->get();

// Get customer with their invoices and preferences
$customer = Customer::with(['invoices.invoiceLines.track', 'categories'])->first();
```

### 1.7.2. Using Tags and Scopes

```php
// Tag an artist with genres
$artist = Artist::first();
$artist->attachTag('rock');
$artist->attachTag('classic-rock');

// Find artists with specific tags
$rockArtists = Artist::withAllTags(['rock'])->get();

// Use scopes for business logic
$popularGenres = Genre::popular(50)->get();
$recentCustomers = Customer::recentCustomers(30)->get();
$publishedArtists = Artist::published()->get();
```

### 1.7.3. Complex Relationships and Calculations

```php
// Get all tracks by a specific artist with duration info
$artist = Artist::first();
$tracks = $artist->tracks()->with('genre')->get();
$totalDuration = $tracks->sum('milliseconds');

// Get employee hierarchy with user stamps
$manager = Employee::with(['subordinates.subordinates', 'createdBy', 'updatedBy'])->first();

// Get playlist with track details and total duration
$playlist = Playlist::with(['tracks.album.artist', 'tracks.genre'])->first();
echo "Playlist duration: {$playlist->formatted_duration}";

// Customer analytics
$customer = Customer::first();
echo "Customer: {$customer->full_name}";
echo "Total spent: \${$customer->total_spent}";
echo "Invoice count: {$customer->invoice_count}";
```

### 1.7.4. Working with Secondary Keys and Slugs

```php
// Create models with automatic public_id and slug generation
$artist = Artist::create(['name' => 'The Beatles']);
echo "Public ID: {$artist->public_id}"; // ULID generated
echo "Slug: {$artist->slug}"; // Generated from public_id

// Route model binding using slugs
Route::get('/artists/{artist}', function (Artist $artist) {
    return $artist; // Automatically resolved by slug
});

// API endpoints using public_id
Route::get('/api/artists/{public_id}', function ($publicId) {
    $artist = Artist::findBySecondaryKeyOrFail($publicId);
    return $artist;
});
```

### 1.7.5. Soft Deletes and User Stamps

```php
// Soft delete with user tracking
$artist = Artist::first();
$artist->delete(); // Sets deleted_at and deleted_by

// Restore soft deleted records
$artist->restore(); // Clears deleted_at

// Query including soft deleted
$allArtists = Artist::withTrashed()->get();
$onlyDeleted = Artist::onlyTrashed()->get();

// Check who created/updated records
echo "Created by: {$artist->createdBy->name}";
echo "Updated by: {$artist->updatedBy->name}";
```

### 1.7.6. Advanced Querying

```php
// Find tracks by duration range
$shortTracks = Track::byDuration(0, 180000)->get(); // Under 3 minutes
$longTracks = Track::byDuration(360000, PHP_INT_MAX)->get(); // Over 6 minutes

// Popular tracks with purchase data
$popularTracks = Track::popular(10)->with('album.artist')->get();

// Customer analytics by country
$usCustomers = Customer::byCountry('USA')
    ->recentCustomers(90)
    ->with('invoices')
    ->get();

// Invoice reporting
$highValueInvoices = Invoice::aboveAmount(50.00)
    ->byDateRange(now()->subMonth(), now())
    ->with('customer')
    ->get();
```

## 1.8. Migration Requirements

Before using these models, ensure your migrations include:

```php
// Required columns for all models
$table->id();
$table->string('public_id')->unique()->index(); // Secondary unique key
$table->string('slug')->unique()->index(); // URL-friendly identifier
$table->timestamps(); // created_at, updated_at
$table->softDeletes(); // deleted_at
$table->userstamps(); // created_by, updated_by, deleted_by

// For models with tags (handled by Spatie Tags package)
// Run: php artisan vendor:publish --provider="Spatie\Tags\TagsServiceProvider" --tag="tags-migrations"
// Then: php artisan migrate
```

## 1.9. Next Steps

After creating these models, you should:

1. **Create Migrations**: See [Chinook Migrations Guide](020-chinook-migrations-guide.md)
2. **Create Factories**: See [Chinook Factories Guide](030-chinook-factories-guide.md)
3. **Create Seeders**: See [Chinook Seeders Guide](040-chinook-seeders-guide.md)
4. **Publish Tag Migrations**: `php artisan vendor:publish --provider="Spatie\Tags\TagsServiceProvider" --tag="tags-migrations"`
5. **Configure User Stamps**: Ensure your User model is properly configured for user stamps

---

## Navigation

**Next →** [Chinook Migrations Guide](020-chinook-migrations-guide.md)
