# 5. Chinook Advanced Features Guide

## 5.1. Overview

This guide covers the advanced enterprise-level features of the Chinook database implementation using Laravel 12 best practices, including comprehensive role-based access control (RBAC) and closure table polymorphic category systems. These features transform the basic music store into a sophisticated, multi-tenant, enterprise-ready application.

**Advanced Laravel 12 Features Covered:**
- **Role-Based Access Control**: Hierarchical permission system with granular controls
- **Closure Table Categories**: Efficient hierarchical categorization system replacing the old Genre model
- **Performance Optimization**: Caching, indexing, and closure table query optimization strategies
- **Modern Authorization**: Custom policies and complex business rules using Laravel 12 patterns
- **Category Tree Management**: Closure table hierarchical data manipulation and UI patterns
- **API Authentication**: Sanctum integration with role-based endpoints
- **Advanced Querying**: Complex closure table category and permission-based queries

## 5.2. Role-Based Access Control (RBAC) System

### 5.2.1. Role Hierarchy and Permissions

The Chinook RBAC system implements a hierarchical role structure with granular permissions:

**Role Hierarchy (from highest to lowest access):**

1. **Super Admin** (`super-admin`)
   - Complete system control
   - User impersonation capabilities
   - System configuration access
   - Audit log management
   - All permissions granted

2. **Admin** (`admin`)
   - Full business operations
   - User management (except super admins)
   - All CRUD operations on Chinook models
   - Report generation and analytics
   - Category management

3. **Manager** (`manager`)
   - Department-specific management
   - Limited user management within department
   - Content editing in assigned areas
   - Departmental reporting
   - Limited category editing

4. **Editor** (`editor`)
   - Content creation and management
   - Artist, album, track management
   - Playlist management
   - Category assignment
   - No user management

5. **Customer Service** (`customer-service`)
   - Customer support operations
   - View customer data and purchase history
   - Process refunds and adjustments
   - Manage customer issues
   - Read-only access to content

6. **User** (`user`)
   - Standard customer operations
   - Browse and purchase content
   - Manage personal playlists
   - Update own profile
   - View public categories

7. **Guest** (`guest`)
   - Public content browsing
   - View artist and album information
   - Access public categories
   - No account required

### 5.2.2. Granular Permission Structure

**Model-Based CRUD Permissions:**
```
view-{model}     - View model records
create-{model}   - Create new model records  
edit-{model}     - Update existing model records
delete-{model}   - Soft delete model records
restore-{model}  - Restore soft deleted records
force-delete-{model} - Permanently delete records
```

**Models:** `artists`, `albums`, `tracks`, `customers`, `employees`, `invoices`, `invoice-lines`, `playlists`, `categories`

**Category-Specific Permissions (Closure Table):**
```
view-categories           - View category trees and closure table relationships
create-categories         - Create new categories
edit-categories          - Update category information
delete-categories        - Delete categories and cleanup closure table
manage-category-hierarchy - Modify closure table parent-child relationships
assign-categories        - Assign categories to models via polymorphic relationships
query-category-descendants - Query category descendants using closure table
query-category-ancestors  - Query category ancestors using closure table
```

**Business Process Permissions:**
```
process-payments         - Handle payment processing
manage-refunds          - Process customer refunds
view-reports            - Access business reports
export-data             - Export system data
view-analytics          - Access analytics dashboards
manage-inventory        - Inventory management
```

**Administrative Permissions:**
```
manage-users            - User account management
assign-roles            - Role assignment to users
revoke-roles           - Remove roles from users
system-configuration   - System settings management
view-audit-logs        - Access audit trail
impersonate-users      - User impersonation
```

### 5.2.3. Permission Seeder Implementation

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Define all models that need CRUD permissions
        $models = [
            'artists', 'albums', 'tracks', 'customers', 'employees', 
            'invoices', 'invoice-lines', 'playlists', 'categories'
        ];

        // Create CRUD permissions for each model
        foreach ($models as $model) {
            $this->createModelPermissions($model);
        }

        // Create category-specific permissions
        $this->createCategoryPermissions();

        // Create business process permissions
        $this->createBusinessPermissions();

        // Create administrative permissions
        $this->createAdministrativePermissions();

        $this->command->info('Created all permissions successfully');
    }

    /**
     * Create CRUD permissions for a model.
     */
    private function createModelPermissions(string $model): void
    {
        $actions = ['view', 'create', 'edit', 'delete', 'restore', 'force-delete'];
        
        foreach ($actions as $action) {
            Permission::create([
                'name' => "{$action}-{$model}",
                'guard_name' => 'web',
            ]);
        }
    }

    /**
     * Create category-specific permissions.
     */
    private function createCategoryPermissions(): void
    {
        $permissions = [
            'view-categories',
            'create-categories', 
            'edit-categories',
            'delete-categories',
            'manage-category-hierarchy',
            'assign-categories',
        ];

        foreach ($permissions as $permission) {
            Permission::create([
                'name' => $permission,
                'guard_name' => 'web',
            ]);
        }
    }

    /**
     * Create business process permissions.
     */
    private function createBusinessPermissions(): void
    {
        $permissions = [
            'process-payments',
            'manage-refunds',
            'view-reports',
            'export-data',
            'view-analytics',
            'manage-inventory',
        ];

        foreach ($permissions as $permission) {
            Permission::create([
                'name' => $permission,
                'guard_name' => 'web',
            ]);
        }
    }

    /**
     * Create administrative permissions.
     */
    private function createAdministrativePermissions(): void
    {
        $permissions = [
            'manage-users',
            'assign-roles',
            'revoke-roles',
            'system-configuration',
            'view-audit-logs',
            'impersonate-users',
        ];

        foreach ($permissions as $permission) {
            Permission::create([
                'name' => $permission,
                'guard_name' => 'web',
            ]);
        }
    }
}
```

### 5.2.4. Role Seeder Implementation

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles with their permission assignments
        $this->createSuperAdminRole();
        $this->createAdminRole();
        $this->createManagerRole();
        $this->createEditorRole();
        $this->createCustomerServiceRole();
        $this->createUserRole();
        $this->createGuestRole();

        $this->command->info('Created all roles with permissions successfully');
    }

    /**
     * Create Super Admin role with all permissions.
     */
    private function createSuperAdminRole(): void
    {
        $role = Role::create(['name' => 'super-admin']);
        
        // Super admin gets ALL permissions
        $role->givePermissionTo(Permission::all());
    }

    /**
     * Create Admin role with business operation permissions.
     */
    private function createAdminRole(): void
    {
        $role = Role::create(['name' => 'admin']);
        
        // All model CRUD permissions except force-delete
        $models = ['artists', 'albums', 'tracks', 'customers', 'employees', 'invoices', 'invoice-lines', 'playlists', 'categories'];
        $actions = ['view', 'create', 'edit', 'delete', 'restore'];
        
        foreach ($models as $model) {
            foreach ($actions as $action) {
                $role->givePermissionTo("{$action}-{$model}");
            }
        }

        // Category permissions
        $role->givePermissionTo([
            'view-categories', 'create-categories', 'edit-categories', 
            'delete-categories', 'manage-category-hierarchy', 'assign-categories'
        ]);

        // Business permissions
        $role->givePermissionTo([
            'process-payments', 'manage-refunds', 'view-reports', 
            'export-data', 'view-analytics', 'manage-inventory'
        ]);

        // Limited admin permissions
        $role->givePermissionTo(['manage-users', 'assign-roles', 'revoke-roles']);
    }

    // Additional role creation methods...
}
```

## 5.3. Closure Table Polymorphic Category System

### 5.3.1. Category Type Implementation

The closure table polymorphic category system replaces the old Genre model with a flexible, efficient hierarchical categorization system supporting multiple category types:

**Category Types:**
- **GENRE**: Music genres (Rock, Jazz, Classical, etc.)
- **MOOD**: Emotional categorization (Energetic, Relaxing, Melancholic, etc.)
- **THEME**: Thematic grouping (Workout, Study, Party, etc.)
- **ERA**: Time periods (1960s, 1970s, 1980s, etc.)
- **INSTRUMENT**: Instrumental focus (Piano, Guitar, Orchestral, etc.)
- **LANGUAGE**: Linguistic categorization (English, Spanish, Instrumental, etc.)
- **OCCASION**: Event-based categorization (Wedding, Birthday, Holiday, etc.)

### 5.3.2. Closure Table Hierarchical Category Management

```php
// Create category hierarchy
$rock = Category::create([
    'name' => 'Rock',
    'type' => CategoryType::GENRE,
    'description' => 'Rock music genre',
    'color' => '#FF6B6B',
    'icon' => 'fas fa-music',
]);

$hardRock = Category::create([
    'name' => 'Hard Rock', 
    'type' => CategoryType::GENRE,
    'description' => 'Hard rock subgenre',
]);

// Establish parent-child relationship using closure table
$hardRock->makeChildOf($rock);

$heavyMetal = Category::create([
    'name' => 'Heavy Metal',
    'type' => CategoryType::GENRE,
    'description' => 'Heavy metal subgenre',
]);

$heavyMetal->makeChildOf($hardRock);

// Efficient closure table queries
echo $heavyMetal->full_name; // "Rock > Hard Rock > Heavy Metal"

// Get all ancestors efficiently using closure table
$ancestors = $heavyMetal->ancestors()->get(); // Uses closure table join

// Get all descendants efficiently using closure table
$descendants = $rock->descendants()->get(); // Uses closure table join

// Get direct children only (depth = 1)
$children = $rock->children()->get(); // Uses closure table with depth = 1

// Get categories at specific depth level
$level2Categories = Category::whereHas('ancestors', function($q) {
    $q->havingRaw('COUNT(*) = 2');
})->get();

// Complex closure table queries
$rockSubgenres = Category::whereHas('ancestors', function($q) use ($rock) {
    $q->where('ancestor_id', $rock->id)->where('depth', '>', 0);
})->get();
```

### 5.3.3. Polymorphic Category Assignment

```php
// Assign categories to different models
$artist = Artist::first();
$album = Album::first();
$track = Track::first();
$playlist = Playlist::first();

// Assign multiple category types to an artist
$artist->categories()->attach([
    $rock->id,           // Genre
    $energetic->id,      // Mood
    $eighties->id,       // Era
    $guitar->id,         // Instrument
]);

// Use helper methods for type-specific assignment
$album->syncCategoriesByType(CategoryType::GENRE, [$rock->id, $pop->id]);
$album->syncCategoriesByType(CategoryType::MOOD, [$energetic->id, $upbeat->id]);
$album->syncCategoriesByType(CategoryType::LANGUAGE, [$english->id]);

// Assign categories with metadata
$track->categories()->attach($workout->id, [
    'metadata' => ['intensity' => 'high', 'bpm' => 140],
    'created_by' => auth()->id(),
]);

// Query by category type
$rockArtists = Artist::byGenre('Rock')->get();
$energeticTracks = Track::byMood('Energetic')->get();
$workoutPlaylists = Playlist::byTheme('Workout')->get();
$eightiesToracks = Track::byEra('1980s')->get();
```

### 5.3.4. Advanced Category Queries

```php
// Find tracks with multiple category criteria
$tracks = Track::whereHas('categories', function ($q) {
    $q->where('type', CategoryType::GENRE)->where('name', 'Rock');
})->whereHas('categories', function ($q) {
    $q->where('type', CategoryType::MOOD)->where('name', 'Energetic');
})->whereHas('categories', function ($q) {
    $q->where('type', CategoryType::ERA)->where('name', '1980s');
})->get();

// Get category statistics
$genreStats = Category::ofType(CategoryType::GENRE)
    ->withCount(['artists', 'albums', 'tracks'])
    ->orderByDesc('tracks_count')
    ->get();

// Find popular categories by purchase data
$popularGenres = Category::ofType(CategoryType::GENRE)
    ->whereHas('tracks.invoiceLines')
    ->withCount('tracks.invoiceLines as purchase_count')
    ->orderByDesc('purchase_count')
    ->limit(10)
    ->get();

// Hierarchical category queries
$rockSubgenres = Category::ofType(CategoryType::GENRE)
    ->where('parent_id', $rock->id)
    ->with('children')
    ->get();

// Get all tracks in a category tree
$allRockTracks = Track::whereHas('categories', function ($q) use ($rock) {
    $q->where('path', 'like', $rock->path . '%');
})->get();
```

## 5.4. Authorization Patterns and Policies

### 5.4.1. Model Policy Implementation

```php
<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Artist;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ArtistPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any artists.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view-artists') || $user->hasRole(['guest', 'user']);
    }

    /**
     * Determine whether the user can view the artist.
     */
    public function view(User $user, Artist $artist): bool
    {
        // Public artists can be viewed by anyone
        if ($artist->is_active) {
            return true;
        }

        // Inactive artists require permission
        return $user->can('view-artists');
    }

    /**
     * Determine whether the user can create artists.
     */
    public function create(User $user): bool
    {
        return $user->can('create-artists');
    }

    /**
     * Determine whether the user can update the artist.
     */
    public function update(User $user, Artist $artist): bool
    {
        // Check basic permission
        if (!$user->can('edit-artists')) {
            return false;
        }

        // Managers can only edit artists they created or in their department
        if ($user->hasRole('manager')) {
            return $artist->created_by === $user->id ||
                   $this->isInSameDepartment($user, $artist);
        }

        return true;
    }

    /**
     * Determine whether the user can delete the artist.
     */
    public function delete(User $user, Artist $artist): bool
    {
        if (!$user->can('delete-artists')) {
            return false;
        }

        // Prevent deletion if artist has albums with sales
        if ($artist->albums()->whereHas('tracks.invoiceLines')->exists()) {
            return $user->hasRole(['admin', 'super-admin']);
        }

        return true;
    }

    /**
     * Determine whether the user can restore the artist.
     */
    public function restore(User $user, Artist $artist): bool
    {
        return $user->can('restore-artists');
    }

    /**
     * Determine whether the user can permanently delete the artist.
     */
    public function forceDelete(User $user, Artist $artist): bool
    {
        return $user->can('force-delete-artists') &&
               $user->hasRole('super-admin');
    }

    /**
     * Determine whether the user can assign categories to the artist.
     */
    public function assignCategories(User $user, Artist $artist): bool
    {
        return $user->can('assign-categories') &&
               $this->update($user, $artist);
    }

    /**
     * Check if user is in the same department as the artist creator.
     */
    private function isInSameDepartment(User $user, Artist $artist): bool
    {
        // Implementation depends on your department structure
        return $user->department === $artist->createdBy->department;
    }
}
```

### 5.4.2. Controller Authorization Examples

```php
<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Artist;
use App\Models\Category;
use App\Enums\CategoryType;
use App\Http\Requests\StoreArtistRequest;
use App\Http\Requests\UpdateArtistRequest;
use Illuminate\Http\Request;

class ArtistController extends Controller
{
    /**
     * Display a listing of artists.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Artist::class);

        $query = Artist::with(['categories', 'albums']);

        // Apply filters based on user permissions
        if (!auth()->user()->can('view-artists')) {
            $query->where('is_active', true);
        }

        // Category filtering
        if ($request->has('genre')) {
            $query->byGenre($request->genre);
        }

        if ($request->has('era')) {
            $query->byEra($request->era);
        }

        return $query->paginate(20);
    }

    /**
     * Store a newly created artist.
     */
    public function store(StoreArtistRequest $request)
    {
        $this->authorize('create', Artist::class);

        $artist = Artist::create($request->validated());

        // Assign categories if provided
        if ($request->has('categories')) {
            $this->authorize('assignCategories', $artist);
            $this->assignCategoriesToArtist($artist, $request->categories);
        }

        return response()->json($artist->load('categories'), 201);
    }

    /**
     * Update the specified artist.
     */
    public function update(UpdateArtistRequest $request, Artist $artist)
    {
        $this->authorize('update', $artist);

        $artist->update($request->validated());

        // Handle category updates
        if ($request->has('categories')) {
            $this->authorize('assignCategories', $artist);
            $this->assignCategoriesToArtist($artist, $request->categories);
        }

        return response()->json($artist->load('categories'));
    }

    /**
     * Remove the specified artist.
     */
    public function destroy(Artist $artist)
    {
        $this->authorize('delete', $artist);

        $artist->delete();

        return response()->json(['message' => 'Artist deleted successfully']);
    }

    /**
     * Assign categories to artist by type.
     */
    private function assignCategoriesToArtist(Artist $artist, array $categories): void
    {
        foreach ($categories as $type => $categoryIds) {
            $categoryType = CategoryType::from($type);

            // Validate that category type is allowed for artists
            if (!in_array($categoryType, CategoryType::forModel(Artist::class))) {
                continue;
            }

            $artist->syncCategoriesByType($categoryType, $categoryIds);
        }
    }
}
```

## 5.5. Closure Table Performance Optimization Strategies

### 5.5.1. Closure Table Query Optimization

The closure table pattern provides excellent performance for hierarchical queries, but proper indexing and query optimization are essential:

```php
// Efficient closure table queries using proper indexes

// Get all descendants with depth information
$descendants = Category::select('categories.*', 'category_closure.depth')
    ->join('category_closure', 'categories.id', '=', 'category_closure.descendant_id')
    ->where('category_closure.ancestor_id', $parentId)
    ->where('category_closure.depth', '>', 0)
    ->orderBy('category_closure.depth')
    ->orderBy('categories.sort_order')
    ->get();

// Get direct children only (most efficient)
$children = Category::select('categories.*')
    ->join('category_closure', 'categories.id', '=', 'category_closure.descendant_id')
    ->where('category_closure.ancestor_id', $parentId)
    ->where('category_closure.depth', 1)
    ->orderBy('categories.sort_order')
    ->get();

// Get category path efficiently
$path = Category::select('categories.*', 'category_closure.depth')
    ->join('category_closure', 'categories.id', '=', 'category_closure.ancestor_id')
    ->where('category_closure.descendant_id', $categoryId)
    ->orderBy('category_closure.depth')
    ->get();
```

### 5.5.2. Category Tree Caching

```php
<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Category;
use App\Enums\CategoryType;
use Illuminate\Support\Facades\Cache;

class CategoryCacheService
{
    private const CACHE_TTL = 3600; // 1 hour

    /**
     * Get cached category tree for a specific type.
     */
    public function getCategoryTree(CategoryType $type): array
    {
        $cacheKey = "category_tree_{$type->value}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($type) {
            return Category::ofType($type)
                ->roots()
                ->with('descendants')
                ->orderBy('sort_order')
                ->get()
                ->toArray();
        });
    }

    /**
     * Get cached popular categories.
     */
    public function getPopularCategories(CategoryType $type, int $limit = 10): array
    {
        $cacheKey = "popular_categories_{$type->value}_{$limit}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($type, $limit) {
            return Category::ofType($type)
                ->withCount(['tracks', 'albums', 'artists'])
                ->orderByDesc('tracks_count')
                ->limit($limit)
                ->get()
                ->toArray();
        });
    }

    /**
     * Clear category caches.
     */
    public function clearCategoryCache(?CategoryType $type = null): void
    {
        if ($type) {
            Cache::forget("category_tree_{$type->value}");
            Cache::forget("popular_categories_{$type->value}_10");
        } else {
            // Clear all category caches
            foreach (CategoryType::cases() as $categoryType) {
                Cache::forget("category_tree_{$categoryType->value}");
                Cache::forget("popular_categories_{$categoryType->value}_10");
            }
        }
    }
}
```

### 5.5.2. Permission Caching Strategy

```php
<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionCacheService
{
    private const CACHE_TTL = 1800; // 30 minutes

    /**
     * Get cached user permissions.
     */
    public function getUserPermissions(User $user): array
    {
        $cacheKey = "user_permissions_{$user->id}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($user) {
            return $user->getAllPermissions()->pluck('name')->toArray();
        });
    }

    /**
     * Check if user has cached permission.
     */
    public function userHasPermission(User $user, string $permission): bool
    {
        $permissions = $this->getUserPermissions($user);
        return in_array($permission, $permissions);
    }

    /**
     * Clear user permission cache.
     */
    public function clearUserPermissionCache(User $user): void
    {
        Cache::forget("user_permissions_{$user->id}");
    }

    /**
     * Clear all permission caches.
     */
    public function clearAllPermissionCaches(): void
    {
        // This would typically be called after role/permission changes
        Cache::flush(); // Or use more targeted cache clearing
    }
}
```

## 5.6. API Authentication and Authorization

### 5.6.1. Laravel Sanctum Integration

```php
<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Artist;
use App\Models\User;
use Illuminate\Http\Request;
use Laravel\Sanctum\HasApiTokens;

class ApiArtistController extends Controller
{
    /**
     * Get artists with role-based filtering.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // Check API permission
        if (!$user || !$user->tokenCan('view-artists')) {
            return response()->json(['error' => 'Insufficient permissions'], 403);
        }

        $query = Artist::with(['categories', 'albums']);

        // Apply role-based filtering
        if (!$user->hasRole(['admin', 'super-admin'])) {
            $query->where('is_active', true);
        }

        // Category filtering
        if ($request->has('genre')) {
            $query->byGenre($request->genre);
        }

        return response()->json($query->paginate(20));
    }

    /**
     * Create artist via API.
     */
    public function store(Request $request)
    {
        $user = $request->user();

        if (!$user || !$user->tokenCan('create-artists')) {
            return response()->json(['error' => 'Insufficient permissions'], 403);
        }

        $this->authorize('create', Artist::class);

        $artist = Artist::create($request->validated());

        return response()->json($artist->load('categories'), 201);
    }
}
```

### 5.6.2. Token Scopes and Permissions

```php
<?php

declare(strict_types=1);

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class ApiTokenController extends Controller
{
    /**
     * Create API token with role-based scopes.
     */
    public function createToken(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
            'device_name' => 'required',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json(['error' => 'Invalid credentials'], 401);
        }

        // Generate token with role-based abilities
        $abilities = $this->getTokenAbilities($user);

        $token = $user->createToken($request->device_name, $abilities);

        return response()->json([
            'token' => $token->plainTextToken,
            'abilities' => $abilities,
            'user' => $user->load('roles.permissions'),
        ]);
    }

    /**
     * Get token abilities based on user roles.
     */
    private function getTokenAbilities(User $user): array
    {
        $abilities = [];

        // Base abilities for all authenticated users
        $abilities[] = 'view-artists';
        $abilities[] = 'view-albums';
        $abilities[] = 'view-tracks';
        $abilities[] = 'view-categories';

        // Role-specific abilities
        if ($user->hasRole(['editor', 'admin', 'super-admin'])) {
            $abilities = array_merge($abilities, [
                'create-artists', 'edit-artists', 'delete-artists',
                'create-albums', 'edit-albums', 'delete-albums',
                'create-tracks', 'edit-tracks', 'delete-tracks',
                'assign-categories',
            ]);
        }

        if ($user->hasRole(['admin', 'super-admin'])) {
            $abilities = array_merge($abilities, [
                'view-customers', 'edit-customers',
                'view-invoices', 'process-payments',
                'view-reports', 'export-data',
            ]);
        }

        if ($user->hasRole('super-admin')) {
            $abilities = array_merge($abilities, [
                'manage-users', 'assign-roles',
                'system-configuration', 'view-audit-logs',
            ]);
        }

        return array_unique($abilities);
    }
}
```

## 5.7. Advanced Querying Techniques

### 5.7.1. Complex Category Queries

```php
<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Track;
use App\Models\Category;
use App\Enums\CategoryType;
use Illuminate\Database\Eloquent\Builder;

class AdvancedQueryService
{
    /**
     * Find tracks matching multiple category criteria.
     */
    public function findTracksByMultipleCategories(array $criteria): Builder
    {
        $query = Track::query();

        foreach ($criteria as $type => $categoryNames) {
            $categoryType = CategoryType::from($type);

            $query->whereHas('categories', function ($q) use ($categoryType, $categoryNames) {
                $q->where('type', $categoryType)
                  ->whereIn('name', (array) $categoryNames);
            });
        }

        return $query;
    }

    /**
     * Get tracks with category hierarchy matching.
     */
    public function findTracksByHierarchy(Category $parentCategory): Builder
    {
        return Track::whereHas('categories', function ($q) use ($parentCategory) {
            $q->where('path', 'like', $parentCategory->path . '%');
        });
    }

    /**
     * Advanced category analytics query.
     */
    public function getCategoryAnalytics(CategoryType $type): array
    {
        $categories = Category::ofType($type)
            ->withCount([
                'tracks',
                'albums',
                'artists',
                'tracks as purchased_tracks' => function ($q) {
                    $q->whereHas('invoiceLines');
                }
            ])
            ->withSum('tracks.invoiceLines', 'unit_price')
            ->get();

        return $categories->map(function ($category) {
            return [
                'id' => $category->id,
                'name' => $category->name,
                'full_name' => $category->full_name,
                'tracks_count' => $category->tracks_count,
                'albums_count' => $category->albums_count,
                'artists_count' => $category->artists_count,
                'purchased_tracks_count' => $category->purchased_tracks_count,
                'total_revenue' => $category->tracks_invoice_lines_sum_unit_price ?? 0,
                'popularity_score' => $this->calculatePopularityScore($category),
            ];
        })->toArray();
    }

    /**
     * Calculate popularity score for a category.
     */
    private function calculatePopularityScore(Category $category): float
    {
        $tracksWeight = 0.3;
        $albumsWeight = 0.2;
        $artistsWeight = 0.2;
        $revenueWeight = 0.3;

        $maxTracks = 1000; // Normalize against expected maximums
        $maxAlbums = 100;
        $maxArtists = 50;
        $maxRevenue = 10000;

        $tracksScore = min($category->tracks_count / $maxTracks, 1) * $tracksWeight;
        $albumsScore = min($category->albums_count / $maxAlbums, 1) * $albumsWeight;
        $artistsScore = min($category->artists_count / $maxArtists, 1) * $artistsWeight;
        $revenueScore = min(($category->tracks_invoice_lines_sum_unit_price ?? 0) / $maxRevenue, 1) * $revenueWeight;

        return round(($tracksScore + $albumsScore + $artistsScore + $revenueScore) * 100, 2);
    }
}
```

## 5.8. Testing Strategies

### 5.8.1. Permission Testing

```php
<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\User;
use App\Models\Artist;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class ArtistAuthorizationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create permissions and roles
        $this->createPermissionsAndRoles();
    }

    /** @test */
    public function admin_can_create_artists()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)
            ->postJson('/api/artists', [
                'name' => 'Test Artist',
                'biography' => 'Test biography',
            ]);

        $response->assertStatus(201);
        $this->assertDatabaseHas('artists', ['name' => 'Test Artist']);
    }

    /** @test */
    public function user_cannot_create_artists()
    {
        $user = User::factory()->create();
        $user->assignRole('user');

        $response = $this->actingAs($user)
            ->postJson('/api/artists', [
                'name' => 'Test Artist',
                'biography' => 'Test biography',
            ]);

        $response->assertStatus(403);
    }

    /** @test */
    public function manager_can_only_edit_own_artists()
    {
        $manager = User::factory()->create();
        $manager->assignRole('manager');

        $ownArtist = Artist::factory()->create(['created_by' => $manager->id]);
        $otherArtist = Artist::factory()->create();

        // Can edit own artist
        $response = $this->actingAs($manager)
            ->putJson("/api/artists/{$ownArtist->id}", [
                'name' => 'Updated Name',
            ]);
        $response->assertStatus(200);

        // Cannot edit other's artist
        $response = $this->actingAs($manager)
            ->putJson("/api/artists/{$otherArtist->id}", [
                'name' => 'Updated Name',
            ]);
        $response->assertStatus(403);
    }

    private function createPermissionsAndRoles(): void
    {
        // Create permissions
        Permission::create(['name' => 'view-artists']);
        Permission::create(['name' => 'create-artists']);
        Permission::create(['name' => 'edit-artists']);
        Permission::create(['name' => 'delete-artists']);

        // Create roles
        $adminRole = Role::create(['name' => 'admin']);
        $managerRole = Role::create(['name' => 'manager']);
        $userRole = Role::create(['name' => 'user']);

        // Assign permissions to roles
        $adminRole->givePermissionTo(['view-artists', 'create-artists', 'edit-artists', 'delete-artists']);
        $managerRole->givePermissionTo(['view-artists', 'create-artists', 'edit-artists']);
        $userRole->givePermissionTo(['view-artists']);
    }
}
```

### 5.8.2. Category System Testing

```php
<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Category;
use App\Models\Artist;
use App\Enums\CategoryType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CategorySystemTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function can_create_hierarchical_categories()
    {
        $rock = Category::factory()->create([
            'name' => 'Rock',
            'type' => CategoryType::GENRE,
        ]);

        $hardRock = Category::factory()->create([
            'name' => 'Hard Rock',
            'type' => CategoryType::GENRE,
        ]);

        $hardRock->makeChildOf($rock);

        $this->assertEquals($rock->id, $hardRock->parent_id);
        $this->assertEquals(1, $hardRock->depth);
        $this->assertTrue($rock->children->contains($hardRock));
    }

    /** @test */
    public function can_assign_categories_to_models()
    {
        $artist = Artist::factory()->create();
        $rockGenre = Category::factory()->create(['type' => CategoryType::GENRE]);
        $energeticMood = Category::factory()->create(['type' => CategoryType::MOOD]);

        $artist->categories()->attach([$rockGenre->id, $energeticMood->id]);

        $this->assertTrue($artist->categories->contains($rockGenre));
        $this->assertTrue($artist->categories->contains($energeticMood));
    }

    /** @test */
    public function can_query_by_category_type()
    {
        $artist = Artist::factory()->create();
        $rockGenre = Category::factory()->create([
            'name' => 'Rock',
            'type' => CategoryType::GENRE,
        ]);

        $artist->categories()->attach($rockGenre->id);

        $rockArtists = Artist::byGenre('Rock')->get();

        $this->assertTrue($rockArtists->contains($artist));
    }
}
```

## 5.9. Best Practices and Recommendations

### 5.9.1. Security Best Practices

1. **Always use authorization checks** in controllers and API endpoints
2. **Implement proper input validation** for category assignments
3. **Use middleware for route protection** with role and permission checks
4. **Cache permissions appropriately** but clear cache when roles change
5. **Audit permission changes** and maintain logs of role assignments
6. **Use database transactions** for complex operations involving multiple models
7. **Validate category hierarchy integrity** to prevent circular references

### 5.9.2. Performance Best Practices

1. **Use eager loading** for category relationships to prevent N+1 queries
2. **Implement caching strategies** for frequently accessed category trees
3. **Use materialized paths** for efficient hierarchical queries
4. **Index polymorphic relationship columns** properly
5. **Consider database partitioning** for large category datasets
6. **Use query optimization** for complex category filtering
7. **Monitor query performance** and optimize slow category queries

### 5.9.3. Maintenance Best Practices

1. **Regular cache clearing** after category structure changes
2. **Database integrity checks** for category hierarchies
3. **Permission audit trails** for compliance and security
4. **Category usage analytics** to identify unused categories
5. **Regular cleanup** of orphaned category assignments
6. **Documentation updates** when adding new category types
7. **Testing coverage** for all authorization scenarios

---

## Navigation

**← Previous:** [Chinook Seeders Guide](040-chinook-seeders-guide.md)

**Next →** [Chinook Index](000-chinook-index.md)
