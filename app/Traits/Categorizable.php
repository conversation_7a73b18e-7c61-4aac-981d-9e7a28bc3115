<?php

declare(strict_types=1);

namespace App\Traits;

use App\Models\Category;
use App\Enums\CategoryType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

/**
 * Categorizable Trait
 * 
 * Provides polymorphic category functionality for Eloquent models.
 * Supports all CategoryType enums and integrates with the closure table Category model.
 * 
 * @package App\Traits
 * <AUTHOR> Laravel 12 Implementation
 * @version 1.0.0
 * 
 * Usage:
 * ```php
 * use App\Traits\Categorizable;
 * 
 * class Artist extends Model
 * {
 *     use Categorizable;
 * }
 * 
 * // Attach categories
 * $artist->attachCategory($rockCategory);
 * $artist->attachCategories([$jazzCategory, $bluesCategory]);
 * 
 * // Query by categories
 * $rockArtists = Artist::withCategories([$rockCategory->id])->get();
 * $moodBasedTracks = Track::withCategoryTypes([CategoryType::MOOD])->get();
 * ```
 */
trait Categorizable
{
    /**
     * Get the polymorphic many-to-many relationship to categories.
     * 
     * @return MorphToMany<Category>
     */
    public function categories(): MorphToMany
    {
        return $this->morphToMany(Category::class, 'categorizable')
            ->withTimestamps()
            ->withPivot(['metadata', 'sort_order', 'is_primary'])
            ->orderBy('pivot_sort_order')
            ->orderBy('categories.sort_order');
    }

    /**
     * Get categories filtered by specific type.
     * 
     * @param CategoryType $type The category type to filter by
     * @return MorphToMany<Category>
     */
    public function categoriesByType(CategoryType $type): MorphToMany
    {
        return $this->categories()->where('categories.type', $type);
    }

    /**
     * Attach a single category to the model.
     * 
     * @param Category $category The category to attach
     * @param array<string, mixed> $attributes Additional pivot attributes
     * @return void
     */
    public function attachCategory(Category $category, array $attributes = []): void
    {
        $defaultAttributes = [
            'sort_order' => 0,
            'is_primary' => false,
            'metadata' => null,
        ];

        $this->categories()->attach($category->id, array_merge($defaultAttributes, $attributes));
    }

    /**
     * Attach multiple categories to the model.
     * 
     * @param array<int|Category> $categories Array of category IDs or Category models
     * @param array<string, mixed> $attributes Default attributes for all attachments
     * @return void
     */
    public function attachCategories(array $categories, array $attributes = []): void
    {
        $categoryData = [];
        $defaultAttributes = [
            'sort_order' => 0,
            'is_primary' => false,
            'metadata' => null,
        ];

        foreach ($categories as $index => $category) {
            $categoryId = $category instanceof Category ? $category->id : $category;
            $categoryData[$categoryId] = array_merge($defaultAttributes, $attributes, [
                'sort_order' => $attributes['sort_order'] ?? $index,
            ]);
        }

        $this->categories()->attach($categoryData);
    }

    /**
     * Detach a single category from the model.
     * 
     * @param Category $category The category to detach
     * @return int Number of affected rows
     */
    public function detachCategory(Category $category): int
    {
        return $this->categories()->detach($category->id);
    }

    /**
     * Sync categories (replace all existing categories).
     * 
     * @param array<int|Category> $categories Array of category IDs or Category models
     * @param array<string, mixed> $attributes Default attributes for all categories
     * @return array<string, array<int>> Sync results
     */
    public function syncCategories(array $categories, array $attributes = []): array
    {
        $categoryData = [];
        $defaultAttributes = [
            'sort_order' => 0,
            'is_primary' => false,
            'metadata' => null,
        ];

        foreach ($categories as $index => $category) {
            $categoryId = $category instanceof Category ? $category->id : $category;
            $categoryData[$categoryId] = array_merge($defaultAttributes, $attributes, [
                'sort_order' => $attributes['sort_order'] ?? $index,
            ]);
        }

        return $this->categories()->sync($categoryData);
    }

    /**
     * Sync categories of a specific type only (preserves other types).
     * 
     * @param CategoryType $type The category type to sync
     * @param array<int|Category> $categories Array of category IDs or Category models
     * @param array<string, mixed> $attributes Default attributes for all categories
     * @return array<string, array<int>> Sync results
     */
    public function syncCategoriesByType(CategoryType $type, array $categories, array $attributes = []): array
    {
        // Get current categories of other types to preserve them
        $otherTypeCategories = $this->categories()
            ->where('categories.type', '!=', $type)
            ->get()
            ->mapWithKeys(function ($category) {
                return [$category->id => [
                    'sort_order' => $category->pivot->sort_order,
                    'is_primary' => $category->pivot->is_primary,
                    'metadata' => $category->pivot->metadata,
                ]];
            })
            ->toArray();

        // Prepare new categories of the specified type
        $newTypeCategories = [];
        $defaultAttributes = [
            'sort_order' => 0,
            'is_primary' => false,
            'metadata' => null,
        ];

        foreach ($categories as $index => $category) {
            $categoryId = $category instanceof Category ? $category->id : $category;
            $newTypeCategories[$categoryId] = array_merge($defaultAttributes, $attributes, [
                'sort_order' => $attributes['sort_order'] ?? $index,
            ]);
        }

        // Merge and sync all categories
        $allCategories = array_merge($otherTypeCategories, $newTypeCategories);
        
        return $this->categories()->sync($allCategories);
    }

    /**
     * Scope to filter models that have specific categories.
     * 
     * @param Builder<static> $query
     * @param array<int> $categoryIds Array of category IDs
     * @return Builder<static>
     */
    public function scopeWithCategories(Builder $query, array $categoryIds): Builder
    {
        return $query->whereHas('categories', function (Builder $q) use ($categoryIds) {
            $q->whereIn('categories.id', $categoryIds);
        });
    }

    /**
     * Scope to filter models that have categories of specific types.
     * 
     * @param Builder<static> $query
     * @param array<CategoryType> $types Array of category types
     * @return Builder<static>
     */
    public function scopeWithCategoryTypes(Builder $query, array $types): Builder
    {
        return $query->whereHas('categories', function (Builder $q) use ($types) {
            $q->whereIn('categories.type', $types);
        });
    }

    /**
     * Scope to filter models that have no categories.
     * 
     * @param Builder<static> $query
     * @return Builder<static>
     */
    public function scopeWithoutCategories(Builder $query): Builder
    {
        return $query->whereDoesntHave('categories');
    }

    /**
     * Check if the model has any categories of the given type.
     * 
     * @param CategoryType $type The category type to check
     * @return bool
     */
    public function hasCategoryType(CategoryType $type): bool
    {
        return $this->categoriesByType($type)->exists();
    }

    /**
     * Get a collection of category names.
     * 
     * @return Collection<int, string>
     */
    public function getCategoryNames(): Collection
    {
        return $this->categories->pluck('name');
    }

    /**
     * Get an array of categories grouped by type with names.
     * 
     * @return array<string, array<string>>
     */
    public function getCategoriesByTypeNames(): array
    {
        return $this->categories
            ->groupBy('type')
            ->map(function (Collection $categories) {
                return $categories->pluck('name')->toArray();
            })
            ->toArray();
    }

    /**
     * Get primary category for a specific type.
     * 
     * @param CategoryType $type The category type
     * @return Category|null
     */
    public function getPrimaryCategory(CategoryType $type): ?Category
    {
        return $this->categoriesByType($type)
            ->wherePivot('is_primary', true)
            ->first();
    }

    /**
     * Set a category as primary for its type.
     * 
     * @param Category $category The category to set as primary
     * @return void
     */
    public function setPrimaryCategory(Category $category): void
    {
        // First, remove primary status from all categories of this type
        $this->categoriesByType($category->type)
            ->updateExistingPivot($this->categories()->getRelatedPivotKeyName(), [
                'is_primary' => false
            ]);

        // Then set the specified category as primary
        $this->categories()->updateExistingPivot($category->id, [
            'is_primary' => true
        ]);
    }

    /**
     * Get categories with their hierarchical path using closure table.
     * 
     * @return Collection<int, Category>
     */
    public function getCategoriesWithPaths(): Collection
    {
        return $this->categories->map(function (Category $category) {
            $category->hierarchical_path = $category->full_name;
            return $category;
        });
    }
}
